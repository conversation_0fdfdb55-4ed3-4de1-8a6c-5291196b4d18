from datetime import datetime, timezone
from enum import Enum
from typing import Any, Dict, List, Optional
from uuid import UUID, uuid4

from fastapi import Query
from pydantic import BaseModel, Field, model_validator

from acva_ai._params import DEFAULT_LLM_PROVIDER, DEFAULT_TRANSCRIPT_PROVIDER
from acva_ai.llm.llm_providers.llm_provider import LLMProvider
from acva_ai.llm.transcript_providers.transcript_provider import TranscriptProvider


class VisitJobStatus(str, Enum):
    """Status enum for visit jobs."""

    CREATED = "created"  # Visit job created, awaiting batches
    RECEIVING_BATCHES = (
        "receiving_batches"  # Batches being uploaded and processed in parallel
    )
    WAITING_FOR_FINISH = (
        "waiting_for_finish"  # All batches uploaded, waiting for processing to complete
    )
    ASSEMBLING_BATCHES = "assembling_batches"  # Concatenating all batch results
    REPORT_GENERATION = "report_generation"  # Running report generation
    COMPLETED = "completed"  # Visit processing complete
    FAILED = "failed"  # Visit processing failed
    CANCELLED = "cancelled"  # Visit processing cancelled


class BatchStatus(str, Enum):
    """Status enum for individual batches within a visit."""

    UPLOADED = "uploaded"  # Batch uploaded, awaiting processing
    PROCESSING = "processing"  # Batch being processed
    COMPLETED = "completed"  # Batch processing complete
    FAILED = "failed"  # Batch processing failed
    CANCELLED = "cancelled"  # Batch processing cancelled


class Batch(BaseModel):
    """Model for individual batches within a visit job."""

    batch_id: UUID = Field(default_factory=uuid4)
    visit_job_id: UUID
    batch_number: int = Field(ge=1, description="Batch sequence number within visit")

    # File information
    file_count: int = Field(ge=1, le=25, description="Number of files in this batch")
    file_names: List[str]
    total_size: int = Field(gt=0, description="Total size of all files in bytes")

    # Processing state
    status: BatchStatus = BatchStatus.UPLOADED

    # Processing results
    processed_audio_path: Optional[str] = None  # Path to processed batch audio
    processing_duration: Optional[float] = None  # Processing time in seconds
    error_message: Optional[str] = None

    # Timing
    created_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    updated_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    processed_at: Optional[datetime] = None

    @model_validator(mode="after")
    def update_timestamp(cls, values):
        """Update the updated_at timestamp."""
        values.updated_at = datetime.now(timezone.utc)
        return values

    @model_validator(mode="after")
    def validate_files(cls, values):
        """Validate file count matches file names."""
        if len(values.file_names) != values.file_count:
            raise ValueError("File count must match number of file names")
        return values


class VisitJob(BaseModel):
    """Model for visit processing jobs that contain multiple batches."""

    visit_job_id: UUID = Field(default_factory=uuid4)
    status: VisitJobStatus = VisitJobStatus.CREATED

    # Configuration
    llm_provider: LLMProvider
    transcript_provider: TranscriptProvider

    # Progress tracking
    batches_received: int = Field(default=0, ge=0)
    batches_processed: int = Field(default=0, ge=0)
    batches_failed: int = Field(default=0, ge=0)
    finish_signal_received: bool = Field(default=False)

    # Results
    assembled_audio_path: Optional[str] = None  # Path to final concatenated audio
    visit_report_id: Optional[str] = None  # ID of the final visit report
    error_message: Optional[str] = None

    # Timing
    created_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    updated_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    first_batch_at: Optional[datetime] = None
    last_batch_at: Optional[datetime] = None
    finished_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None

    @model_validator(mode="after")
    def update_timestamp(cls, values):
        """Update the updated_at timestamp."""
        values.updated_at = datetime.now(timezone.utc)
        return values

    @model_validator(mode="after")
    def validate_batches(cls, values):
        """Validate batch counts."""
        total_batches = values.batches_received
        if values.batches_processed + values.batches_failed > total_batches:
            raise ValueError("Processed + failed batches cannot exceed total batches")
        return values

    @property
    def total_batches(self) -> int:
        """Get total number of batches received."""
        return self.batches_received

    @property
    def progress_percentage(self) -> float:
        """Calculate progress percentage."""
        if self.batches_received == 0:
            return 0.0
        return (self.batches_processed / self.batches_received) * 100

    @property
    def is_ready_for_assembly(self) -> bool:
        """Check if the visit job is ready for assembly."""
        return (
            self.finish_signal_received
            and self.batches_received > 0
            and self.batches_received == self.batches_processed
            and self.batches_failed == 0
            and self.status
            not in [
                VisitJobStatus.CREATED,
                VisitJobStatus.RECEIVING_BATCHES,  # Still receiving batches or already processing
                VisitJobStatus.ASSEMBLING_BATCHES,  # Already assembling
                VisitJobStatus.REPORT_GENERATION,  # Already processing medical
                VisitJobStatus.COMPLETED,  # Already completed
                VisitJobStatus.FAILED,  # Already failed
                VisitJobStatus.CANCELLED,  # Cancelled
            ]
        )

    @property
    def should_fail(self) -> bool:
        """Check if visit should be marked as failed."""
        return self.batches_failed > 0 or self.status == VisitJobStatus.FAILED


class VisitJobCreate(BaseModel):
    """Model for creating a new visit job."""

    llm_provider: LLMProvider = Query(
        DEFAULT_LLM_PROVIDER,
        description="LLM provider to use (openai, azure)",
    )
    transcript_provider: TranscriptProvider = Query(
        DEFAULT_TRANSCRIPT_PROVIDER,
        description="Transcript provider to use (openai, elevenlabs, azure)",
    )


class VisitJobResponse(BaseModel):
    """Response model for visit job operations."""

    task_id: UUID
    status: VisitJobStatus
    batches_received: int
    batches_processed: int
    batches_failed: int
    progress_percentage: float
    finish_signal_received: bool
    created_at: datetime
    updated_at: datetime


class BatchCreate(BaseModel):
    """Model for creating a new batch within a visit job."""

    visit_job_id: UUID
    batch_number: int = Field(ge=1, description="Batch sequence number")
    file_names: List[str]
    total_size: int = Field(gt=0, description="Total size of all files")


class BatchUpdate(BaseModel):
    """Model for updating batch status."""

    status: Optional[BatchStatus] = None
    processed_audio_path: Optional[str] = None
    processing_duration: Optional[float] = None
    error_message: Optional[str] = None


class VisitJobUpdate(BaseModel):
    """Model for updating visit job status."""

    status: Optional[VisitJobStatus] = None
    batches_processed: Optional[int] = None
    batches_failed: Optional[int] = None
    finish_signal_received: Optional[bool] = None
    assembled_audio_path: Optional[str] = None
    visit_report_id: Optional[str] = None
    error_message: Optional[str] = None


class BatchResponse(BaseModel):
    """Response model for batch operations."""

    batch_id: UUID
    task_id: UUID
    batch_number: int
    file_count: int
    status: BatchStatus
    created_at: datetime
    updated_at: datetime
    error_message: Optional[str] = None


class VisitJobStatusResponse(BaseModel):
    """Detailed response model for visit job status."""

    task_id: UUID
    status: VisitJobStatus
    progress: Dict[str, Any]
    batches: List[BatchResponse]
    timestamps: Dict[str, Optional[datetime]]
