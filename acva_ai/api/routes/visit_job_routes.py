import asyncio
import io
import logging
import uuid
from datetime import datetime, timezone
from typing import List, Optional

from fastapi import APIRouter, Depends, File, HTTPException, UploadFile
from fastapi.responses import JSONResponse

from acva_ai.api.routes.dlq_routes import dlq_router
from acva_ai.database.mongo import mongo_instance
from acva_ai.models.visit_job import (
    <PERSON><PERSON>,
    BatchResponse,
    VisitJob,
    VisitJobCreate,
    VisitJobResponse,
    VisitJobStatus,
    VisitJobStatusResponse,
)
from acva_ai.services.minio_client import MinioClient
from acva_ai.services.queue_config import queue_config
from acva_ai.services.visit_job_coordinator import VisitJobCoordinator
from acva_ai.utils.audio_utils import SUPPORTED_MIME_TYPES
from acva_ai.utils.security_service import SecurityService

logger = logging.getLogger(__name__)

visit_job_router = APIRouter(dependencies=[Depends(SecurityService.get_api_key)])


@visit_job_router.post("/create", response_model=VisitJobResponse)
async def create_visit_job(
    visit_job_data: VisitJobCreate,
):
    """
    Create a new visit job for processing multiple batches of audio files.

    This endpoint creates a container for all batches belonging to one medical visit.
    The client will use the returned task_id for all subsequent batch uploads.

    Args:
        visit_job_data: Visit job configuration.
                       llm_provider provider to use (openai, azure)
                       transcript_provider provider to use (openai, elevenlabs, azure)

    Returns:
        VisitJobResponse with task_id and initial status


    """
    try:
        coordinator = VisitJobCoordinator()
        visit_job_id = coordinator.create_visit_job(
            llm_provider=visit_job_data.llm_provider,
            transcript_provider=visit_job_data.transcript_provider,
        )

        job_data = mongo_instance.get_visit_job(visit_job_id)
        if not job_data:
            raise HTTPException(status_code=500, detail="Failed to create visit job")

        visit_job = VisitJob(**job_data)

        return VisitJobResponse(
            task_id=visit_job.visit_job_id,
            status=visit_job.status,
            created_at=visit_job.created_at,
            updated_at=visit_job.updated_at,
            batches_received=visit_job.batches_received,
            batches_processed=visit_job.batches_processed,
            batches_failed=visit_job.batches_failed,
            progress_percentage=visit_job.progress_percentage,
            finish_signal_received=visit_job.finish_signal_received,
        )

    except ValueError as e:
        logger.error(f"Validation error creating visit job: {e}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Error creating visit job: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


async def _validate_visit_job(visit_job_id: str):
    """Validate that visit job exists and is in correct status."""
    job_data = mongo_instance.get_visit_job(visit_job_id)
    if not job_data:
        raise HTTPException(status_code=404, detail="Visit job not found")

    visit_job = VisitJob(**job_data)

    if visit_job.status not in [
        VisitJobStatus.CREATED,
        VisitJobStatus.RECEIVING_BATCHES,
    ]:
        raise HTTPException(
            status_code=400,
            detail=f"Cannot submit batch to visit job in status: {visit_job.status}",
        )


async def _validate_and_process_files(
    files: List[UploadFile], visit_job_id: str, batch_number: int
) -> tuple[List[str], List[str], int]:
    """Validate files and upload them to MinIO in parallel."""
    if len(files) > 25:
        raise HTTPException(
            status_code=400, detail="Maximum 25 files allowed per batch"
        )

    if len(files) == 0:
        raise HTTPException(status_code=400, detail="At least one file is required")

    file_names = []
    total_size = 0
    max_file_size = 1024 * 1024 * 1024  # 1GB in bytes

    file_data = []
    for file in files:
        if not file.filename:
            raise HTTPException(status_code=400, detail="All files must have filenames")

        if file.content_type not in SUPPORTED_MIME_TYPES:
            raise HTTPException(
                status_code=400,
                detail=f"Unsupported file format: {file.content_type}. Supported formats: {', '.join(SUPPORTED_MIME_TYPES)}",
            )

        file_size = file.size
        if file_size is None:
            file_content = await file.read()
            file_size = len(file_content)
            await file.seek(0)

        if file_size > max_file_size:
            raise HTTPException(
                status_code=400,
                detail=f"File {file.filename} is {file_size / (1024*1024):.1f}MB. Maximum file size is 1GB",
            )

        total_size += file_size
        file_names.append(file.filename)

        file_content = await file.read()
        object_name = f"raw_audio/{visit_job_id}/{batch_number}/{file.filename}"

        file_data.append(
            {
                "object_name": object_name,
                "content": file_content,
                "size": file_size,
                "content_type": file.content_type,
            }
        )

    minio_client = MinioClient()

    async def upload_single_file(file_info):
        """Upload a single file to MinIO in a thread executor."""
        loop = asyncio.get_event_loop()
        await loop.run_in_executor(
            None,
            minio_client.upload_file,
            file_info["object_name"],
            io.BytesIO(file_info["content"]),
            file_info["size"],
            file_info["content_type"],
        )
        return file_info["object_name"]

    minio_paths = await asyncio.gather(
        *[upload_single_file(file_info) for file_info in file_data]
    )

    return file_names, minio_paths, total_size


@visit_job_router.post("/{task_id}/batches", response_model=BatchResponse)
async def submit_batch(
    task_id: str,
    files: List[UploadFile] = File(...),
    batch_number: Optional[int] = None,
):
    """
    Submit a batch of audio files for a visit job.

    This endpoint accepts up to 25 audio files as one batch within a visit job.
    Multiple batches can be submitted for the same visit job.

    Args:
        task_id: The task ID to associate this batch with
        files: List of audio files (max 25, supported formats: WebM, MP3, WAV, M4A)
        batch_number: Optional batch sequence number (auto-assigned if not provided)

    Returns:
        BatchResponse with batch details and processing status
    """
    try:
        await _validate_visit_job(task_id)

        coordinator = VisitJobCoordinator()
        if batch_number is None:
            existing_batches = mongo_instance.get_batches_by_visit_job(task_id)
            batch_number = len(existing_batches) + 1

        file_names, minio_paths, total_size = await _validate_and_process_files(
            files, task_id, batch_number
        )

        batch = await coordinator.submit_batch(
            visit_job_id=task_id,
            batch_number=batch_number,
            minio_paths=minio_paths,
            file_names=file_names,
            total_size=total_size,
        )

        return BatchResponse(
            batch_id=batch.batch_id,
            task_id=batch.visit_job_id,
            batch_number=batch.batch_number,
            file_count=batch.file_count,
            status=batch.status,
            created_at=batch.created_at,
            updated_at=batch.updated_at,
            error_message=batch.error_message,
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error submitting batch for visit job {task_id}: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@visit_job_router.post("/{task_id}/finish")
async def finish_visit_job(task_id: str):
    """
    Signal that all batches for a visit job have been submitted.

    It triggers the report generation process once all batches are processed.

    Args:
        task_id: The task ID to finalize

    Returns:
        Success message with current status
    """
    try:
        job_data = mongo_instance.get_visit_job(task_id)
        if not job_data:
            raise HTTPException(status_code=404, detail="Visit job not found")

        visit_job = VisitJob(**job_data)

        if visit_job.status not in [
            VisitJobStatus.CREATED,
            VisitJobStatus.RECEIVING_BATCHES,
        ]:
            raise HTTPException(
                status_code=400,
                detail=f"Cannot finish visit job in status: {visit_job.status}",
            )

        coordinator = VisitJobCoordinator()
        coordinator.signal_finish(task_id)

        return JSONResponse(
            {
                "message": "Finish signal received",
                "task_id": task_id,
                "status": "waiting_for_finish",
            }
        )

    except Exception as e:
        logger.error(f"Error finishing visit job {task_id}: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@visit_job_router.get("/{task_id}/status", response_model=VisitJobStatusResponse)
async def get_visit_job_status(task_id: str):
    """
    Get the detailed status of a visit job including all batches.

    Args:
        task_id: The task ID

    Returns:
        VisitJobStatusResponse with detailed status and progress information
    """
    try:
        job_data = mongo_instance.get_visit_job(task_id)
        if not job_data:
            raise HTTPException(status_code=404, detail="Visit job not found")

        visit_job = VisitJob(**job_data)

        batches_data = mongo_instance.get_batches_by_visit_job(task_id)
        batches = []

        for batch_data in batches_data:
            batch = Batch(**batch_data)
            batches.append(
                BatchResponse(
                    batch_id=batch.batch_id,
                    task_id=batch.visit_job_id,
                    batch_number=batch.batch_number,
                    file_count=batch.file_count,
                    status=batch.status,
                    created_at=batch.created_at,
                    updated_at=batch.updated_at,
                    error_message=batch.error_message,
                )
            )

        progress = {
            "total_batches": visit_job.batches_received,
            "batches_processed": visit_job.batches_processed,
            "batches_failed": visit_job.batches_failed,
            "progress_percentage": visit_job.progress_percentage,
            "is_ready_for_assembly": visit_job.is_ready_for_assembly,
        }

        timestamps = {
            "created_at": visit_job.created_at,
            "updated_at": visit_job.updated_at,
            "first_batch_at": visit_job.first_batch_at,
            "last_batch_at": visit_job.last_batch_at,
            "finished_at": visit_job.finished_at,
            "completed_at": visit_job.completed_at,
        }

        return VisitJobStatusResponse(
            task_id=visit_job.visit_job_id,
            status=visit_job.status,
            progress=progress,
            batches=batches,
            timestamps=timestamps,
        )

    except Exception as e:
        logger.error(f"Error getting visit job status {task_id}: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@visit_job_router.get("/{task_id}/batches", response_model=List[BatchResponse])
async def get_visit_job_batches(task_id: str):
    """
    Get all batches for a visit job.

    Args:
        task_id: The task ID

    Returns:
        List of BatchResponse with batch details
    """
    try:
        job_data = mongo_instance.get_visit_job(task_id)
        if not job_data:
            raise HTTPException(status_code=404, detail="Visit job not found")

        batches_data = mongo_instance.get_batches_by_visit_job(task_id)
        batches = []

        for batch_data in batches_data:
            batch = Batch(**batch_data)
            batches.append(
                BatchResponse(
                    batch_id=batch.batch_id,
                    task_id=batch.visit_job_id,
                    batch_number=batch.batch_number,
                    file_count=batch.file_count,
                    status=batch.status,
                    created_at=batch.created_at,
                    updated_at=batch.updated_at,
                    error_message=batch.error_message,
                )
            )

        return batches

    except Exception as e:
        logger.error(f"Error getting batches for visit job {task_id}: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@visit_job_router.get("/metrics")
async def get_queue_metrics():
    """
    Get basic queue statistics.

    Returns basic queue information including job counts.
    """
    try:
        stats = queue_config.get_queue_stats()
        return JSONResponse(
            {
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "queues": stats,
            }
        )
    except Exception as e:
        logger.error(f"Error getting queue stats: {e}")
        raise HTTPException(status_code=500, detail="Failed to retrieve queue stats")


visit_job_router.include_router(dlq_router, prefix="/dlq")
