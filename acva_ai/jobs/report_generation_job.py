"""
Report Generation Job

Handles complete visit processing including batch assembly and report generation.
"""

import asyncio
import logging
import os
import tempfile
from datetime import datetime, timezone
from typing import List

from pydub import AudioSegment

from acva_ai.database.mongo import mongo_instance
from acva_ai.models.visit_job import <PERSON><PERSON><PERSON>tat<PERSON>, VisitJob, VisitJobStatus
from acva_ai.pipeline.main import process_visit as pipeline_process_visit
from acva_ai.services.base_job import BaseJob, register_job
from acva_ai.services.minio_client import MinioClient
from acva_ai.services.thread_pool_service import get_thread_pool_service
from acva_ai.utils.audio_utils import normalize_audio_segment

logger = logging.getLogger(__name__)


@register_job
class ProcessVisitReportGenerationJob(BaseJob):
    """Job for processing complete visits including batch assembly and report generation."""

    name = "Process Visit Report Generation Pipeline"
    description = (
        "Assemble batches and process complete visit through report generation pipeline"
    )
    timeout = 3600  # 1 hour
    queue_name = "report_generation"
    max_retries = 2
    retry_delay = 120

    def __init__(self):
        super().__init__()
        self.minio_client = MinioClient(bucket_name="acva-visit-results")
        self.thread_pool_service = get_thread_pool_service()

    def execute(self, visit_job_id: str) -> str:
        """Assemble batches and process complete visit through report generation pipeline."""
        return asyncio.run(self._process_async(visit_job_id))

    def _calculate_processing_duration(self) -> float:
        """Calculates the processing duration."""
        return (datetime.now(timezone.utc) - self.start_time).total_seconds()

    async def _process_async(self, visit_job_id: str) -> str:
        """Async implementation including batch assembly and report generation processing."""
        try:
            visit_job_data = mongo_instance.get_visit_job(visit_job_id)
            if not visit_job_data:
                raise ValueError(f"Visit job {visit_job_id} not found")

            self._update_job_progress(10, "Starting report generation process")
            mongo_instance.update_visit_job(
                visit_job_id, {"status": VisitJobStatus.REPORT_GENERATION.value}
            )

            self._update_job_progress(20, "Assembling batch audio files")
            normalized_audio = await self._assemble_batch_audio_async(visit_job_id)

            visit_job = VisitJob(**visit_job_data)

            self._update_job_progress(
                50, "Processing visit through report generation pipeline"
            )
            await pipeline_process_visit(
                audio_segment=normalized_audio,
                llm_provider=visit_job.llm_provider,
                transcript_provider=visit_job.transcript_provider,
                task_id=visit_job_id,
            )

            processing_duration = self._calculate_processing_duration()

            self._update_job_progress(90, "Finalizing visit job completion")
            update_data = {
                "status": VisitJobStatus.COMPLETED.value,
                "completed_at": datetime.now(timezone.utc),
                "processing_duration": processing_duration,
            }
            mongo_instance.update_visit_job(visit_job_id, update_data)

            self._update_job_progress(95, "Cleaning up batch files")
            self._cleanup_batch_files(visit_job_id)

            self.logger.info(
                f"Successfully processed visit job {visit_job_id} through report generation pipeline in {processing_duration:.2f}s"
            )
            return visit_job_id

        except Exception as e:
            processing_duration = self._calculate_processing_duration()

            try:
                update_data = {
                    "status": VisitJobStatus.FAILED.value,
                    "error_message": str(e),
                    "completed_at": datetime.now(timezone.utc),
                    "processing_duration": processing_duration,
                }
                mongo_instance.update_visit_job(visit_job_id, update_data)
                self.logger.info(f"Updated visit job {visit_job_id} status to FAILED")
            except Exception as update_error:
                self.logger.error(
                    f"Failed to update visit job status to FAILED: {update_error}"
                )

            if "timeout" in str(e).lower() or "connection" in str(e).lower():
                self.logger.warning(f"Retryable error in report generation: {e}")

            raise

    async def _assemble_batch_audio_async(self, visit_job_id: str) -> AudioSegment:
        """Async implementation of batch assembly with parallel downloads."""
        try:
            batches = mongo_instance.get_batches_by_visit_job(visit_job_id)
            completed_batches = [
                b for b in batches if b.get("status") == BatchStatus.COMPLETED.value
            ]

            if not completed_batches:
                raise ValueError(
                    f"No completed batches found for visit job {visit_job_id}"
                )

            completed_batches.sort(key=lambda x: x.get("batch_number", 0))

            self._update_job_progress(
                30, f"Downloading {len(completed_batches)} batch audio files"
            )
            temp_dir = os.path.join(tempfile.gettempdir(), "acva_ai", "visit_assembly")
            os.makedirs(temp_dir, exist_ok=True)

            def download_and_process_batch(batch: dict):
                """Download and process a single batch audio file."""
                batch_number = batch.get("batch_number")
                processed_audio_path = batch.get("processed_audio_path")

                if not processed_audio_path:
                    raise ValueError(
                        f"No processed audio path found for batch {batch_number}"
                    )

                try:
                    file_data = self.minio_client.download_file(processed_audio_path)

                    temp_file_path = os.path.join(temp_dir, f"batch_{batch_number}.wav")
                    with open(temp_file_path, "wb") as f:
                        f.write(file_data)

                    audio_segment = AudioSegment.from_file(temp_file_path)
                    os.remove(temp_file_path)

                    self.logger.debug(
                        f"Loaded batch {batch_number} audio: {len(audio_segment)}ms"
                    )

                    return batch_number, audio_segment

                except Exception as e:
                    raise ValueError(f"Failed to load batch {batch_number} audio: {e}")

            args_list = [batch for batch in completed_batches]
            results = await self.thread_pool_service.gather_with_asyncio_to_thread(
                download_and_process_batch, args_list
            )
            results.sort(key=lambda x: x[0] or 0)
            audio_segments = [segment for _, segment in results]

            if not audio_segments:
                raise ValueError("No valid audio segments found for assembly")

            self._update_job_progress(
                40,
                f"Assembling {len(audio_segments)} audio segments into complete visit audio",
            )
            complete_audio = sum(audio_segments, AudioSegment.silent(duration=0))
            normalized_audio = normalize_audio_segment(complete_audio)

            self.logger.info(
                f"Assembled complete visit audio from {len(audio_segments)} batches: {len(normalized_audio)}ms"
            )

            return normalized_audio

        except Exception as e:
            raise ValueError(f"Failed to assemble batch audio: {e}")

    def _cleanup_batch_files(self, visit_job_id: str):
        """Clean up batch files after successful job completion."""
        try:
            batches = mongo_instance.get_batches_by_visit_job(visit_job_id)
            completed_batches = [
                b for b in batches if b.get("status") == BatchStatus.COMPLETED.value
            ]

            batch_ids_cleaned = []
            for batch in completed_batches:
                batch_id = str(batch.get("batch_id", ""))
                if batch_id:
                    try:
                        self.minio_client.cleanup_batch_files(batch_id)
                        batch_ids_cleaned.append(batch_id)
                    except Exception as batch_cleanup_error:
                        self.logger.warning(
                            f"Failed to cleanup batch {batch_id}: {batch_cleanup_error}"
                        )

            if batch_ids_cleaned:
                self.logger.info(
                    f"Successfully cleaned up {len(batch_ids_cleaned)} batch files for visit job {visit_job_id}"
                )
        except Exception as cleanup_error:
            self.logger.warning(
                f"Failed to cleanup batch files for visit job {visit_job_id}: {cleanup_error}"
            )


def ProcessVisitReportGenerationJob_rq_job(*args, **kwargs):
    """RQ job function for ProcessVisitReportGenerationJob."""
    job_instance = ProcessVisitReportGenerationJob()
    return job_instance.run(*args, **kwargs)
