services:
  backend:
    container_name: backend
    build:
      context: .
      dockerfile: resources/Dockerfile
    ports:
      - "8000:8000"
    env_file:
      - .env
    environment:
      - MONGO_DB_CONNECTION_STRING=mongodb://mongodb:27017
      - REDIS_USE_SENTINEL=true
      - REDIS_SENTINEL_HOSTS=acva-ai-sentinel-1:26379,acva-ai-sentinel-2:26379,acva-ai-sentinel-3:26379
      - REDIS_SENTINEL_SERVICE_NAME=acva-redis
      - QDRANT_SERVER=qdrant_db
      - MINIO_SERVER=minio
      - REDIS_HOST=redis-master
      - TMP_DATA_DIR=/tmp
    volumes:
      - ${TMP_DATA_DIR}:/tmp
      - ./data:/app/data
    depends_on:
      - qdrant_db
      - redis-master
      - redis-replica
      - sentinel
      - mongodb
    networks:
      - default
      - redis-network
    command: >
      /bin/sh -c "gunicorn acva_ai.api.main:app -b 0.0.0.0:8000 --workers ${API_WORKERS} --threads ${API_WORKER_THREADS} -k 'uvicorn.workers.UvicornWorker' --timeout 600"

  qdrant_db:
    image: qdrant/qdrant
    container_name: qdrant_db
    ports:
      - "6333:6333"
    volumes:
      - ${QDRANT_FOLDER}:/qdrant/storage
    healthcheck:
      test:
        [
          "CMD-SHELL",
          "timeout 10s bash -c '</dev/tcp/localhost/6333' || exit 1",
        ]
      interval: 5s
      timeout: 10s
      retries: 10

  minio:
    image: minio/minio:latest
    container_name: minio
    ports:
      - "9000:9000"
      - "9001:9001"
    environment:
      MINIO_ROOT_USER: $MINIO_ROOT_USER
      MINIO_ROOT_PASSWORD: $MINIO_ROOT_PASSWORD
    command: server /data --console-address ":9001"
    volumes:
      - ${MINIO_FOLDER}:/data
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 20s
      retries: 3

  # Redis Master-Slave with Sentinel Setup
  redis-master:
    image: redis:7-alpine
    container_name: redis-master
    ports:
      - "${REDIS_MASTER_PORT}:6379"
    volumes:
      - ${REDIS_FOLDER}/master:/data
      - ./redis-configs/redis-master.conf:/usr/local/etc/redis/redis.conf
    command: redis-server /usr/local/etc/redis/redis.conf
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 5s
      timeout: 3s
      retries: 5
    networks:
      - redis-network

  redis-replica:
    image: redis:7-alpine
    container_name: redis-replica
    ports:
      - "${REDIS_REPLICA_PORT}:6379"
    volumes:
      - ${REDIS_FOLDER}/replica:/data
      - ./redis-configs/redis-replica.conf:/usr/local/etc/redis/redis.conf
      - ./redis-configs/wait-for-replica.sh:/usr/local/bin/wait-for-replica.sh
    command: /usr/local/bin/wait-for-replica.sh redis-master 6379 /usr/local/etc/redis/redis.conf
    depends_on:
      - redis-master
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 5s
      timeout: 3s
      retries: 5
    networks:
      - redis-network

  sentinel:
    image: redis:7-alpine
    volumes:
      - ./redis-configs/sentinel.conf:/usr/local/etc/redis/sentinel.conf
      - ./redis-configs/wait-for-redis.sh:/usr/local/bin/wait-for-redis.sh
    command: /usr/local/bin/wait-for-redis.sh redis-master 6379 /usr/local/etc/redis/sentinel.conf
    depends_on:
      redis-master:
        condition: service_healthy
      redis-replica:
        condition: service_healthy
    restart: unless-stopped
    networks:
      - redis-network
    deploy:
      replicas: 3

  mongodb:
    image: mongo:latest
    container_name: mongodb
    ports:
      - "27017:27017"
    volumes:
      - ${MONGO_FOLDER}:/data/db
    healthcheck:
      test:
        ["CMD", "mongosh", "--eval", "db.runCommand({ ping: 1 })", "--quiet"]
      interval: 10s
      timeout: 10s
      retries: 10

  rq-worker:
    build:
      context: .
      dockerfile: resources/Dockerfile
    env_file:
      - .env
    environment:
      - MONGO_DB_CONNECTION_STRING=mongodb://mongodb:27017
      - REDIS_USE_SENTINEL=true
      - REDIS_SENTINEL_HOSTS=acva-ai-sentinel-1:26379,acva-ai-sentinel-2:26379,acva-ai-sentinel-3:26379
      - REDIS_SENTINEL_SERVICE_NAME=acva-redis
      - QDRANT_SERVER=qdrant_db
      - MINIO_SERVER=minio
      - REDIS_HOST=redis-master
      - TMP_DATA_DIR=/tmp
    volumes:
      - ${TMP_DATA_DIR}:/tmp
      - ./data:/app/data
    depends_on:
      sentinel:
        condition: service_started
      qdrant_db:
        condition: service_healthy
      mongodb:
        condition: service_healthy
    networks:
      - default
      - redis-network
    deploy:
      replicas: 3
    command: >
      /bin/sh -c "python -m acva_ai.services.worker_service"

  rq-dashboard:
    container_name: rq-dashboard
    image: cjlapao/rq-dashboard:latest
    ports:
      - "9181:9181"
    volumes:
      - ./redis-configs/rq-dashboard-entrypoint.py:/usr/local/bin/rq-dashboard-entrypoint.py
    environment:
      - SENTINEL_HOSTS=acva-ai-sentinel-1:26379,acva-ai-sentinel-2:26379,acva-ai-sentinel-3:26379
      - SENTINEL_SERVICE_NAME=acva-redis
    entrypoint: ["python3", "/usr/local/bin/rq-dashboard-entrypoint.py"]
    depends_on:
      sentinel:
        condition: service_started
    networks:
      - redis-network

volumes:
  minio_data:
    driver: local

networks:
  redis-network:
    driver: bridge
