"""
ElevenLabs transcript provider implementation.
"""

import asyncio
import hashlib
import json
import os
from pathlib import Path
from typing import Dict, Optional, Union

from elevenlabs import ElevenLabs
from pydub import AudioSegment

from acva_ai._params import (
    ELEVENLABS_API_KEY,
    ELEVENLABS_MAX_AUDIO_SIZE,
    ELEVENLABS_MODEL_ID,
)
from acva_ai.llm.llm_cache import LLM_AUDIO_CACHE_DIR
from acva_ai.llm.transcript_providers.transcript_helpers import (
    AudioFileError,
    AudioTranscriptionError,
    TranscriptionResponse,
    calculate_usage_cost_from_file,
    load_cached_response,
    save_to_cache,
    standardize_response,
    validate_file_size,
)
from acva_ai.utils.usage import ResponseUsage

os.makedirs(LLM_AUDIO_CACHE_DIR, exist_ok=True)


def _validate_convert_audio_file(audio_file_path: str) -> str:
    """
    Validate audio file exists and convert it to PCM_S16LE_16 format.

    For pcm_s16le_16, the input audio must be:
    - 16-bit PCM at a 16kHz sample rate
    - Single channel (mono)
    - Little-endian byte order

    Args:
        audio_file_path: Path to the original audio file

    Returns:
        str: Path to the converted audio file in the required format

    Raises:
        AudioFileError: If there are issues with the audio file or conversion
    """
    try:
        audio_path = Path(audio_file_path)
        if not audio_path.exists():
            raise AudioFileError(f"Audio file not found: {audio_file_path}")

        if not audio_path.is_file():
            raise AudioFileError(f"Path is not a file: {audio_file_path}")

        # Check if file is readable
        try:
            with open(audio_path, "rb") as f:
                if not f.read(1):  # Try to read at least 1 byte
                    raise AudioFileError(f"Audio file is empty: {audio_file_path}")
        except PermissionError:
            raise AudioFileError(f"Permission denied reading file: {audio_file_path}")
        except OSError as e:
            raise AudioFileError(f"Error reading audio file: {e}")

        # Load audio file using pydub
        try:
            audio = AudioSegment.from_file(audio_file_path)
        except Exception as e:
            raise AudioFileError(f"Could not load audio file {audio_file_path}: {e}")

        # Convert to required PCM_S16LE_16 format
        try:
            # Convert to mono (single channel)
            audio = audio.set_channels(1)

            # Set sample rate to 16kHz
            audio = audio.set_frame_rate(16000)

            # Set sample width to 16-bit (2 bytes)
            audio = audio.set_sample_width(2)

            # Generate output path for converted file
            original_path = Path(audio_file_path)
            converted_filename = f"{original_path.stem}_pcm_s16le_16.wav"
            converted_path = original_path.parent / converted_filename

            # Export with PCM S16LE codec (16-bit PCM, little-endian)
            audio.export(str(converted_path), format="wav", codec="pcm_s16le")

            print(f"Audio converted to PCM_S16LE_16 format: {converted_path}")
            return str(converted_path)

        except Exception as e:
            raise AudioFileError(f"Error converting audio to PCM_S16LE_16 format: {e}")

    except Exception as e:
        if isinstance(e, (AudioFileError, AudioTranscriptionError)):
            raise
        raise AudioFileError(f"Unexpected error validating/converting audio file: {e}")


class ElevenLabsTranscriptProvider:
    """
    ElevenLabs transcript provider implementation.

    This provider uses the ElevenLabs Speech-to-Text API for audio transcription.
    It supports PCM_S16LE_16 format conversion and provides detailed word-level data.
    """

    def __init__(self):
        """Initialize the ElevenLabs transcript provider."""
        self.provider_name = "elevenlabs"
        self.model_id = ELEVENLABS_MODEL_ID

        if not ELEVENLABS_API_KEY:
            raise AudioTranscriptionError(
                "ELEVENLABS_API_KEY environment variable is not set"
            )

    def get_max_file_size_mb(self) -> float:
        """Get the maximum file size supported by ElevenLabs."""
        return ELEVENLABS_MAX_AUDIO_SIZE

    def _group_words_into_sentences_with_speakers(self, words_data: list) -> dict:
        """
        Group words into sentences and organize them by speaker.

        Args:
            words_data: List of word dictionaries with speaker_id information

        Returns:
            Dict with format: {"speaker_1": {"sentences": ["sentence1", "sentence2"]}}
        """
        if not words_data:
            return {}

        # First, group words into individual sentences
        sentences = []
        current_sentence = {
            "text": "",
            "speaker_id": None,
            "start": None,
            "end": None,
            "words": [],
        }

        for word in words_data:
            word_text = word.get("text", "")
            word_speaker = word.get("speaker_id")
            word_start = word.get("start")
            word_end = word.get("end")

            # Initialize sentence if it's empty
            if not current_sentence["words"]:
                current_sentence["speaker_id"] = word_speaker
                current_sentence["start"] = word_start

            # Add word to current sentence
            current_sentence["words"].append(word)
            current_sentence["end"] = word_end

            # Check if this word ends a sentence (contains sentence-ending punctuation)
            is_sentence_end = any(
                punct in word_text for punct in [".", "!", "?", ":", ";"]
            )

            # Check if speaker changed (start new sentence on speaker change)
            speaker_changed = (
                word_speaker != current_sentence["speaker_id"]
                and word_speaker is not None
                and current_sentence["speaker_id"] is not None
            )

            if is_sentence_end or speaker_changed:
                # Finalize current sentence
                if current_sentence["words"]:
                    current_sentence["text"] = " ".join(
                        [w.get("text", "") for w in current_sentence["words"]]
                    ).strip()
                    sentences.append(current_sentence.copy())

                # Start new sentence
                current_sentence = {
                    "text": "",
                    "speaker_id": word_speaker if speaker_changed else None,
                    "start": word_start if speaker_changed else None,
                    "end": None,
                    "words": [],
                }

                # If speaker changed, add the current word to the new sentence
                if speaker_changed:
                    current_sentence["words"].append(word)
                    current_sentence["end"] = word_end

        # Add the last sentence if it has content
        if current_sentence["words"]:
            current_sentence["text"] = " ".join(
                [w.get("text", "") for w in current_sentence["words"]]
            ).strip()
            sentences.append(current_sentence)

        # Now group sentences by speaker in the desired format
        sentences_by_speaker = {}
        for sentence in sentences:
            speaker_id = sentence.get("speaker_id", "unknown")

            # Convert speaker_0 to speaker_1, speaker_1 to speaker_2, etc.
            if speaker_id.startswith("speaker_"):
                try:
                    speaker_num = int(speaker_id.split("_")[1])
                    formatted_speaker_id = f"speaker_{speaker_num + 1}"
                except (IndexError, ValueError):
                    formatted_speaker_id = speaker_id
            else:
                formatted_speaker_id = speaker_id

            if formatted_speaker_id not in sentences_by_speaker:
                sentences_by_speaker[formatted_speaker_id] = {"sentences": []}

            # Add just the text of the sentence
            sentences_by_speaker[formatted_speaker_id]["sentences"].append(
                sentence["text"]
            )

        return sentences_by_speaker

    async def transcribe_audio_file(
        self,
        audio_file_path: str,
        language: Optional[str] = "ron",
        use_cache: bool = True,
        response_usage: Optional[ResponseUsage] = None,
        max_file_size_mb: Optional[float] = None,
        diarize: bool = False,
        **kwargs,
    ) -> TranscriptionResponse:
        """
        Transcribe audio from a file path using ElevenLabs.

        Args:
            audio_file_path: Path to the audio file
            language: Optional language code
            use_cache: Whether to use cached responses
            response_usage: Optional ResponseUsage object to track costs
            max_file_size_mb: Maximum allowed file size in megabytes
            diarize: Whether to enable speaker diarization and return structured response
            **kwargs: Additional ElevenLabs-specific parameters

        Returns:
            TranscriptionResponse object with text and optional speaker data

        Raises:
            AudioFileError: If there are issues with the audio file
            AudioTranscriptionError: If there are issues with the transcription API
        """
        try:
            result = await self.call_audio_transcription_from_file_async(
                audio_file_path=audio_file_path,
                language=language,
                use_cache=use_cache,
                response_usage=response_usage,
                max_file_size_mb=max_file_size_mb or self.get_max_file_size_mb(),
                diarize=diarize,
                **kwargs,
            )

            # Always return TranscriptionResponse object
            speakers_data = result.get("sentences_by_speaker") if diarize else None
            return TranscriptionResponse(
                text=result.get("text", ""), speakers_data=speakers_data
            )

        except Exception as e:
            if isinstance(e, (AudioFileError, AudioTranscriptionError)):
                raise
            raise AudioTranscriptionError(f"ElevenLabs transcription error: {e}")

    async def transcribe_audio_segment(
        self,
        audio_segment: AudioSegment,
        language: Optional[str] = "ron",
        use_cache: bool = False,
        response_usage: Optional[ResponseUsage] = None,
        max_file_size_mb: Optional[float] = None,
        diarize: bool = False,
        **kwargs,
    ) -> TranscriptionResponse:
        """
        Transcribe audio from an AudioSegment using ElevenLabs.

        Note: ElevenLabs provider doesn't have a direct AudioSegment method,
        so we'll save the segment to a temporary file and use the file method.

        Args:
            audio_segment: AudioSegment object to transcribe
            language: Optional language code
            use_cache: Whether to use cached responses
            response_usage: Optional ResponseUsage object to track costs
            max_file_size_mb: Maximum allowed file size in megabytes
            diarize: Whether to enable speaker diarization and return structured response
            **kwargs: Additional ElevenLabs-specific parameters

        Returns:
            TranscriptionResponse object with text and optional speaker data

        Raises:
            AudioFileError: If there are issues with the AudioSegment
            AudioTranscriptionError: If there are issues with the transcription API
        """
        import tempfile

        try:
            # Create a temporary file for the audio segment
            with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as temp_file:
                temp_path = temp_file.name

            try:
                # Export the audio segment to the temporary file
                audio_segment.export(temp_path, format="wav")

                # Use the file transcription method
                result = await self.transcribe_audio_file(
                    audio_file_path=temp_path,
                    language=language,
                    use_cache=use_cache,
                    response_usage=response_usage,
                    max_file_size_mb=max_file_size_mb,
                    diarize=diarize,
                    **kwargs,
                )
                return result

            finally:
                # Clean up the temporary file
                try:
                    if os.path.exists(temp_path):
                        os.remove(temp_path)
                except Exception as cleanup_error:
                    print(
                        f"Warning: Could not cleanup temp file {temp_path}: {cleanup_error}"
                    )

        except Exception as e:
            if isinstance(e, (AudioFileError, AudioTranscriptionError)):
                raise
            raise AudioTranscriptionError(
                f"ElevenLabs AudioSegment transcription error: {e}"
            )

    async def call_transcription_async(
        self,
        audio_input: Union[str, AudioSegment],
        language: Optional[str] = "ron",
        use_cache: bool = True,
        response_usage: Optional[ResponseUsage] = None,
        max_file_size_mb: Optional[float] = None,
        **kwargs,
    ) -> Dict:
        """
        Call ElevenLabs transcription API and return full response data.

        Args:
            audio_input: Either file path (str) or AudioSegment object
            language: Optional language code
            use_cache: Whether to use cached responses
            response_usage: Optional ResponseUsage object to track costs
            max_file_size_mb: Maximum allowed file size in megabytes
            **kwargs: Additional ElevenLabs-specific parameters

        Returns:
            Dict containing full transcription response data

        Raises:
            AudioFileError: If there are issues with the audio input
            AudioTranscriptionError: If there are issues with the transcription API
        """
        try:
            if isinstance(audio_input, str):
                # File path input
                result = await self.call_audio_transcription_from_file_async(
                    audio_file_path=audio_input,
                    language=language,
                    use_cache=use_cache,
                    response_usage=response_usage,
                    max_file_size_mb=max_file_size_mb or self.get_max_file_size_mb(),
                    **kwargs,
                )
            else:
                # AudioSegment input - convert to file first
                import tempfile

                with tempfile.NamedTemporaryFile(
                    suffix=".wav", delete=False
                ) as temp_file:
                    temp_path = temp_file.name

                try:
                    audio_input.export(temp_path, format="wav")
                    result = await self.call_audio_transcription_from_file_async(
                        audio_file_path=temp_path,
                        language=language,
                        use_cache=use_cache,
                        response_usage=response_usage,
                        max_file_size_mb=max_file_size_mb
                        or self.get_max_file_size_mb(),
                        **kwargs,
                    )
                finally:
                    try:
                        if os.path.exists(temp_path):
                            os.remove(temp_path)
                    except Exception as cleanup_error:
                        print(
                            f"Warning: Could not cleanup temp file {temp_path}: {cleanup_error}"
                        )

            # Standardize the response format
            return standardize_response(result, self.provider_name)

        except Exception as e:
            if isinstance(e, (AudioFileError, AudioTranscriptionError)):
                raise
            raise AudioTranscriptionError(f"ElevenLabs API call error: {e}")

    async def call_audio_transcription_from_file_async(
        self,
        audio_file_path: str,
        language: Optional[str] = "ron",
        temperature: float = 0,
        use_cache: bool = False,
        response_usage: Optional[ResponseUsage] = None,
        max_file_size_mb: float = ELEVENLABS_MAX_AUDIO_SIZE,
        cleanup_converted_file: bool = True,
        diarize: bool = False,
    ) -> Dict:
        """
        Asynchronous call to ElevenLabs Audio Transcription from file path with caching.

        The audio file will be automatically converted to PCM_S16LE_16 format (16-bit PCM,
        16kHz sample rate, mono, little-endian) before transcription.

        Args:
            audio_file_path: Path to the audio file
            language: Optional language code
            temperature: Sampling temperature (0 to 2)
            use_cache: Whether to use cached responses
            response_usage: Optional ResponseUsage object to track costs
            max_file_size_mb: Maximum allowed file size in megabytes (default: 1000.0)
            cleanup_converted_file: Whether to delete the converted file after processing (default: True)
            diarize: Whether to enable speaker diarization

        Returns:
            Dict containing transcription results

        Raises:
            AudioFileError: If there are issues with the audio file or if file is too large
            AudioTranscriptionError: If there are issues with the transcription API
        """
        try:
            # Validate file size before processing
            validate_file_size(audio_file_path, max_file_size_mb)

            # Validate audio file exists and convert to PCM_S16LE_16 format
            converted_audio_path = _validate_convert_audio_file(audio_file_path)

            # Generate cache key based on converted file content
            with open(converted_audio_path, "rb") as f:
                file_content = f.read()
            content_hash = hashlib.md5(file_content).hexdigest()

            # Setup cache
            cache_key = f"{content_hash}_{self.model_id}_{language or 'auto'}"
            cache_filename = f"{cache_key}.json"
            cache_filepath = os.path.join(LLM_AUDIO_CACHE_DIR, cache_filename)

            # Try to load from cache
            if use_cache:
                cached_response = load_cached_response(cache_filepath)
                if cached_response is not None:
                    return cached_response

            client = ElevenLabs(api_key=ELEVENLABS_API_KEY)

            # Make transcription request using ElevenLabs library
            try:
                # Run the synchronous client method in a thread pool
                import time

                start = time.time()

                # Prepare API call parameters
                api_params = {
                    "model_id": self.model_id,
                    "file": open(converted_audio_path, "rb"),
                    "temperature": temperature,
                    "diarize": diarize,
                }

                # Only include language_code if it's not None (for auto-detection)
                if language is not None:
                    api_params["language_code"] = language

                response = await asyncio.to_thread(
                    client.speech_to_text.convert, **api_params
                )
                print(f"Response time: {time.time() - start:.2f} seconds")

                # Convert response to dict format
                words_data = [
                    {
                        "text": word.text,
                        "type": word.type,
                        "start": word.start,
                        "end": word.end,
                        "speaker_id": getattr(word, "speaker_id", None),
                        "logprob": getattr(word, "logprob", None),
                    }
                    for word in (response.words or [])
                ]

                response_data = {
                    "text": response.text,
                    "language_code": response.language_code,
                    "language_probability": response.language_probability,
                    "words": words_data,
                }

                # Only include speaker data when diarization is enabled
                if diarize:
                    sentences_by_speaker = (
                        self._group_words_into_sentences_with_speakers(words_data)
                    )
                    response_data["sentences_by_speaker"] = sentences_by_speaker

            except Exception as e:
                raise AudioTranscriptionError(f"ElevenLabs API error: {e}")

            # Calculate usage if requested
            if response_usage is not None:
                await calculate_usage_cost_from_file(
                    self.model_id, converted_audio_path, response_data, response_usage
                )

            # Cache the response
            if use_cache:
                save_to_cache(cache_filepath, response_data)

            # Cleanup converted file if requested
            if cleanup_converted_file:
                try:
                    if os.path.exists(converted_audio_path):
                        os.remove(converted_audio_path)
                        print(
                            f"Cleaned up converted audio file: {converted_audio_path}"
                        )
                except Exception as cleanup_error:
                    print(
                        f"Warning: Could not cleanup converted file {converted_audio_path}: {cleanup_error}"
                    )

            return response_data

        except (AudioFileError, AudioTranscriptionError):
            raise
        except Exception as e:
            raise AudioTranscriptionError(
                f"Unexpected error in call_audio_transcription_from_file_async: {e}"
            )

    async def _generate_transcription(
        self,
        audio_segment: AudioSegment,
        language: str = "ron",
        response_usage: Optional[ResponseUsage] = None,
    ) -> str:
        """
        ElevenLabs-specific transcription generation.

        Args:
            audio_segment: AudioSegment to transcribe
            language: Language code for transcription
            response_usage: Optional ResponseUsage object to track costs

        Returns:
            Transcribed text as string
        """

        results = await self.transcribe_audio_segment(
            audio_segment=audio_segment,
            language=language,
            response_usage=response_usage,
        )

        # Extract text from TranscriptionResponse object
        return results.text if hasattr(results, "text") else str(results)
