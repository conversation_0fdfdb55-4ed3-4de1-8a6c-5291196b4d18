import asyncio
import logging
import traceback
from typing import Dict, Optional, Tuple

from acva_ai.database import mongo_instance
from acva_ai.llm.transcript_orchestrator import TranscriptOrchestrator
from acva_ai.models.processing_status import ProcessingStatus
from acva_ai.models.visit_report import VisitReport
from acva_ai.utils.usage import ResponseUsage

# Set up logging
logger = logging.getLogger(__name__)

from pydub import AudioSegment


def transform_speaker_data_format(speakers_data: Dict) -> Dict:
    """
    Transform speaker data from ElevenLabs format to the desired format.

    Input format:
    {
      "speaker_0": [
        {"text": "Hello", "start": 0.0, "end": 1.0},
        {"text": "World", "start": 1.0, "end": 2.0}
      ]
    }

    Output format:
    {
      "speaker_1": {
        "sentences": ["Hello", "World"]
      }
    }
    """
    if not speakers_data:
        return {}

    transformed = {}
    for speaker_id, sentences_list in speakers_data.items():
        # Convert speaker_0 to speaker_1, speaker_1 to speaker_2, etc.
        if speaker_id.startswith("speaker_"):
            try:
                speaker_num = int(speaker_id.split("_")[1])
                new_speaker_id = f"speaker_{speaker_num + 1}"
            except (IndexError, ValueError):
                new_speaker_id = speaker_id
        else:
            new_speaker_id = speaker_id

        # Extract just the text from each sentence object
        sentences = []
        for sentence in sentences_list:
            if isinstance(sentence, dict) and "text" in sentence:
                sentences.append(sentence["text"])
            elif isinstance(sentence, str):
                sentences.append(sentence)

        transformed[new_speaker_id] = {"sentences": sentences}

    return transformed


async def _generate_transcription(
    audio_segment: AudioSegment,
    transcript_orchestrator: TranscriptOrchestrator,
    langauge: str = "ro",
    response_usage: Optional[ResponseUsage] = None,
) -> str:

    if transcript_orchestrator is None:
        raise ValueError("Orchestrator provider must be specified")

    # Use provider-specific transcription generation
    result_tuple = await transcript_orchestrator.generate_transcription(
        audio_segment=audio_segment,
        language=langauge,
        response_usage=response_usage,
    )

    # Extract text from tuple (text, chunks_status_dict)
    result_transcript = (
        result_tuple[0] if isinstance(result_tuple, tuple) else result_tuple
    )
    return result_transcript


async def generate_transcription(
    task_id: str,
    audio_segment: AudioSegment,
    processing_status: Optional[ProcessingStatus],
    visit_report: Optional[VisitReport],
    transcript_orchestrator: TranscriptOrchestrator,
    langauge: str = "ro",
    response_usage: Optional[ResponseUsage] = None,
    diarize: bool = False,
):
    if transcript_orchestrator is None:
        raise ValueError("Orchestrator provider must be specified")

    """
    Processes an AudioSegment and generates the trasncription
    """
    logger.info(f"[Task {task_id}] Starting transcription generation")
    processing_status.start_stage("transcription")
    mongo_instance.update_processing_status(task_id, processing_status.dict())

    try:
        if diarize:
            # Use diarization-enabled transcription
            transcript_response = (
                await transcript_orchestrator.transcribe_with_diarization(
                    audio_segment=audio_segment,
                    language=langauge,
                    response_usage=response_usage,
                    diarize=diarize,
                )
            )
            # Extract text and store speaker data if available
            if hasattr(transcript_response, "to_dict"):
                transcript_data = transcript_response.to_dict()
                result_transcript = transcript_data["text"]
                # Store speaker data in visit report metadata for future use
                if transcript_data.get("speakers_data"):
                    if not visit_report.metadata:
                        visit_report.metadata = {}
                    visit_report.metadata["speakers_data"] = transcript_data[
                        "speakers_data"
                    ]
            else:
                # Fallback for string response
                result_transcript = str(transcript_response)
        else:
            # Use regular transcription without diarization
            result_transcript = await _generate_transcription(
                audio_segment=audio_segment,
                langauge=langauge,
                response_usage=response_usage,
                transcript_orchestrator=transcript_orchestrator,
            )

        visit_report.raw_transcript = result_transcript
        processing_status.complete_stage("transcription")
        logger.info(f"[Task {task_id}] Completed transcription successfully")

    except Exception as e:
        stack_trace = traceback.format_exc()
        processing_status.fail_stage("transcription", e, stack_trace)
        processing_status.finalize()
        logger.error(f"[Task {task_id}] Error generating transcript {e}\n{stack_trace}")

    finally:
        mongo_instance.update_processing_status(task_id, processing_status.dict())


def test():
    sample_audio_path = ".data/demo/1e4cd7fa-b4a1-47ce-99aa-abdf66885b2b.wav"
    audio_segment = AudioSegment.from_file(sample_audio_path)

    # Process the audio segment
    result_transcript = asyncio.run(
        _generate_transcription(audio_segment=audio_segment)
    )
    print(result_transcript)


if __name__ == "__main__":
    test()
