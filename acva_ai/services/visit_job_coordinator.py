import logging
import uuid
from datetime import datetime, timezone
from typing import Any, Dict, List, Optional
from uuid import UUID

from pydantic import Field

from acva_ai.database.mongo import mongo_instance
from acva_ai.jobs.batch_processing_job import ProcessVisitBatchAudioJob
from acva_ai.jobs.report_generation_job import ProcessVisitReportGenerationJob
from acva_ai.llm.llm_providers.llm_provider import LLMProvider
from acva_ai.llm.transcript_providers.transcript_provider import TranscriptProvider
from acva_ai.models.visit_job import Batch, BatchStatus, VisitJob, VisitJobStatus
from acva_ai.services.queue_config import queue_config

logger = logging.getLogger(__name__)


class VisitJobCoordinator:
    """Coordinator for managing visit jobs and their batches."""

    def __init__(self):
        self.logger = logging.getLogger(__name__)

    def create_visit_job(
        self,
        llm_provider: LLMProvider,
        transcript_provider: TranscriptProvider,
    ) -> str:
        """Create a new visit job and return its ID."""
        try:
            visit_job = VisitJob(
                llm_provider=llm_provider,
                transcript_provider=transcript_provider,
            )

            job_data = visit_job.model_dump()
            mongo_instance.create_visit_job(job_data)

            visit_job_id = str(visit_job.visit_job_id)
            self.logger.info(f"Created visit job {visit_job_id}")

            return visit_job_id

        except Exception as e:
            self.logger.error(f"Failed to create visit job: {e}")
            raise

    async def submit_batch(
        self,
        visit_job_id: str,
        batch_number: int,
        minio_paths: List[str],
        file_names: List[str],
        total_size: int,
    ) -> Batch:
        """Submit a batch of files for a visit job."""
        try:
            job_data = mongo_instance.get_visit_job(visit_job_id)
            if not job_data:
                raise ValueError(f"Visit job {visit_job_id} not found")

            visit_job = VisitJob(**job_data)

            existing_batches = mongo_instance.get_batches_by_visit_job(visit_job_id)
            existing_numbers = [b.get("batch_number") for b in existing_batches]
            if batch_number in existing_numbers:
                raise ValueError(
                    f"Batch number {batch_number} already exists for visit job {visit_job_id}"
                )

            batch = Batch(
                visit_job_id=UUID(visit_job_id),
                batch_id=uuid.uuid4(),
                batch_number=batch_number,
                file_count=len(minio_paths),
                file_names=file_names,
                total_size=total_size,
            )

            batch_data = batch.model_dump()
            mongo_instance.create_batch(batch_data)

            update_data = {
                "batches_received": visit_job.batches_received + 1,
                "status": VisitJobStatus.RECEIVING_BATCHES.value,
            }

            if visit_job.first_batch_at is None:
                update_data["first_batch_at"] = datetime.now(timezone.utc)

            update_data["last_batch_at"] = datetime.now(timezone.utc)

            mongo_instance.update_visit_job(visit_job_id, update_data)

            await self._queue_batch_processing(str(batch.batch_id), minio_paths)

            self.logger.info(
                f"Submitted batch {batch_number} for visit job {visit_job_id}"
            )

            return batch

        except Exception as e:
            self.logger.error(
                f"Failed to submit batch for visit job {visit_job_id}: {e}"
            )
            raise

    def signal_finish(self, visit_job_id: str) -> bool:
        """Signal that all batches have been submitted for a visit job."""
        try:
            update_data = {
                "finish_signal_received": True,
                "finished_at": datetime.now(timezone.utc),
                "status": VisitJobStatus.WAITING_FOR_FINISH.value,
            }
            mongo_instance.update_visit_job(visit_job_id, update_data)

            self.logger.info(f"Finish signal received for visit job {visit_job_id}")

            self._check_assembly_readiness(visit_job_id)

            return True

        except Exception as e:
            self.logger.error(
                f"Failed to signal finish for visit job {visit_job_id}: {e}"
            )
            raise

    def update_batch_status(
        self,
        batch_id: str,
        status: BatchStatus,
        processed_audio_path: Optional[str] = None,
        processing_duration: Optional[float] = None,
        error_message: Optional[str] = None,
    ) -> None:
        """Update batch status and visit job progress."""
        try:
            batch_data = mongo_instance.get_batch(batch_id)
            if not batch_data:
                raise ValueError(f"Batch {batch_id} not found")

            batch = Batch(**batch_data)
            visit_job_id = str(batch.visit_job_id)

            update_data = {
                "status": status.value,
                "processed_at": datetime.now(timezone.utc),
            }

            if processed_audio_path:
                update_data["processed_audio_path"] = processed_audio_path
            if processing_duration:
                update_data["processing_duration"] = processing_duration
            if error_message:
                update_data["error_message"] = error_message

            mongo_instance.update_batch(batch_id, update_data)

            if status in [BatchStatus.COMPLETED, BatchStatus.FAILED]:
                self._update_visit_job_progress(
                    visit_job_id, status == BatchStatus.COMPLETED
                )

            self.logger.info(f"Updated batch {batch_id} status to {status.value}")

        except Exception as e:
            self.logger.error(f"Failed to update batch status for {batch_id}: {e}")
            raise

    async def _queue_batch_processing(
        self,
        batch_id: str,
        minio_paths: List[str],
    ) -> None:
        """Queue a batch for processing."""
        try:
            batch_data = mongo_instance.get_batch(batch_id)
            if not batch_data:
                raise ValueError(f"Batch {batch_id} not found")

            batch = Batch(**batch_data)
            visit_job_id = str(batch.visit_job_id)

            visit_job_data = mongo_instance.get_visit_job(visit_job_id)
            visit_job = VisitJob(**visit_job_data) if visit_job_data else None

            job_metadata = {
                "visit_job_id": visit_job_id,
                "batch_id": batch_id,
                "batch_number": batch.batch_number,
                "file_count": len(minio_paths),
                "total_size_bytes": batch.total_size,
                "job_type": "batch_processing",
                "llm_provider": (
                    visit_job.llm_provider.value if visit_job else "unknown"
                ),
                "transcript_provider": (
                    visit_job.transcript_provider.value if visit_job else "unknown"
                ),
                "created_by": "visit_job_coordinator",
            }

            rq_job_id = f"{visit_job_id}-batch-{batch.batch_number:03d}-{batch_id}"
            job_description = f"Visit {visit_job_id} batch {batch.batch_number} ({len(minio_paths)} files, {batch.total_size/1024/1024:.1f}MB)"

            ProcessVisitBatchAudioJob.enqueue(
                queue_config.batch_queue,
                batch_id,
                minio_paths,
                job_id=rq_job_id,
                meta=job_metadata,
                description=job_description,
            )

            self.logger.info(
                f"Queued batch {batch_id} for processing with RQ job ID {rq_job_id}"
            )

        except Exception as e:
            self.logger.error(f"Failed to queue batch {batch_id} for processing: {e}")
            raise

    def _update_visit_job_progress(self, visit_job_id: str, success: bool) -> None:
        """Update visit job progress after batch completion."""
        try:
            job_data = mongo_instance.get_visit_job(visit_job_id)
            if not job_data:
                raise ValueError(f"Visit job {visit_job_id} not found")

            visit_job = VisitJob(**job_data)

            if success:
                new_processed = visit_job.batches_processed + 1
                update_data = {
                    "batches_processed": new_processed,
                }
            else:
                new_failed = visit_job.batches_failed + 1
                update_data = {
                    "batches_failed": new_failed,
                    "status": VisitJobStatus.FAILED.value,
                }

            mongo_instance.update_visit_job(visit_job_id, update_data)

            if success:
                self._check_assembly_readiness(visit_job_id)

        except Exception as e:
            self.logger.error(
                f"Failed to update visit job progress for {visit_job_id}: {e}"
            )

    def _check_assembly_readiness(self, visit_job_id: str) -> None:
        """Check if visit job is ready for assembly and trigger if ready."""
        try:
            job_data = mongo_instance.get_visit_job(visit_job_id)
            if not job_data:
                return

            visit_job = VisitJob(**job_data)

            if visit_job.is_ready_for_assembly:
                self.logger.info(f"Visit job {visit_job_id} is ready for assembly")
                self._trigger_report_generation(visit_job_id)

        except Exception as e:
            self.logger.error(
                f"Failed to check assembly readiness for {visit_job_id}: {e}"
            )

    def _trigger_report_generation(self, visit_job_id: str) -> None:
        """Trigger report generation process for a visit job."""
        try:
            update_data = {"status": VisitJobStatus.ASSEMBLING_BATCHES.value}
            mongo_instance.update_visit_job(visit_job_id, update_data)

            visit_job_data = mongo_instance.get_visit_job(visit_job_id)
            visit_job = VisitJob(**visit_job_data) if visit_job_data else None

            batches = mongo_instance.get_batches_by_visit_job(visit_job_id)
            completed_batches = [
                b for b in batches if b.get("status") == BatchStatus.COMPLETED.value
            ]

            job_metadata = {
                "visit_job_id": visit_job_id,
                "job_type": "report_generation",
                "total_batches": len(batches),
                "completed_batches": len(completed_batches),
                "llm_provider": (
                    visit_job.llm_provider.value if visit_job else "unknown"
                ),
                "transcript_provider": (
                    visit_job.transcript_provider.value if visit_job else "unknown"
                ),
                "created_by": "visit_job_coordinator",
            }

            rq_job_id = f"report-{visit_job_id[:8]}-{len(completed_batches)}batches"
            job_description = f"Visit {visit_job_id} ({len(completed_batches)} batches)"

            ProcessVisitReportGenerationJob.enqueue(
                queue_config.report_generation_queue,
                visit_job_id,
                job_id=rq_job_id,
                meta=job_metadata,
                description=job_description,
            )

            self.logger.info(
                f"Triggered report generation for visit job {visit_job_id} with RQ job ID {rq_job_id}"
            )

        except Exception as e:
            self.logger.error(
                f"Failed to trigger report generation for visit job {visit_job_id}: {e}"
            )
            raise

    def get_visit_job_progress(self, visit_job_id: str) -> Dict[str, Any]:
        """Get detailed progress information for a visit job."""
        try:
            job_data = mongo_instance.get_visit_job(visit_job_id)
            if not job_data:
                return {}

            visit_job = VisitJob(**job_data)
            batches = mongo_instance.get_batches_by_visit_job(visit_job_id)

            completed_batches = len(
                [b for b in batches if b.get("status") == BatchStatus.COMPLETED.value]
            )
            failed_batches = len(
                [b for b in batches if b.get("status") == BatchStatus.FAILED.value]
            )

            return {
                "visit_job_id": visit_job_id,
                "status": visit_job.status,
                "progress": {
                    "batches_received": visit_job.batches_received,
                    "batches_processed": completed_batches,
                    "batches_failed": failed_batches,
                    "progress_percentage": visit_job.progress_percentage,
                    "is_ready_for_assembly": visit_job.is_ready_for_assembly,
                },
                "timestamps": {
                    "created_at": visit_job.created_at,
                    "first_batch_at": visit_job.first_batch_at,
                    "last_batch_at": visit_job.last_batch_at,
                    "finished_at": visit_job.finished_at,
                    "completed_at": visit_job.completed_at,
                },
                "finish_signal_received": visit_job.finish_signal_received,
            }

        except Exception as e:
            self.logger.error(
                f"Failed to get visit job progress for {visit_job_id}: {e}"
            )
            return {}


visit_job_coordinator = VisitJobCoordinator()
