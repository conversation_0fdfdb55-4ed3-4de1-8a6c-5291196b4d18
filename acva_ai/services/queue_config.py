import logging
import os
import uuid
from datetime import datetime, timezone
from typing import Any, Dict, List, Optional

from redis import Redis
from redis.sentinel import Sentinel
from rq import Queue, Worker

from acva_ai._params import (
    REDIS_CONNECTION_POOL_SIZE,
    REDIS_DB,
    REDIS_HOST,
    REDIS_MASTER_PORT,
    REDIS_PASSWORD,
    REDIS_SENTINEL_HOSTS,
    REDIS_SENTINEL_SERVICE_NAME,
    REDIS_SOCKET_CONNECT_TIMEOUT,
    REDIS_SOCKET_TIMEOUT,
    REDIS_USE_SENTINEL,
)

logger = logging.getLogger(__name__)


class QueueConfig:
    """Configuration and management for RQ (Redis Queue) infrastructure with Redis Sentinel support."""

    def __init__(self):
        # Redis Sentinel configuration
        self.use_sentinel = REDIS_USE_SENTINEL
        self.sentinel_service_name = REDIS_SENTINEL_SERVICE_NAME
        self.sentinel_hosts = REDIS_SENTINEL_HOSTS

        # Fallback to direct Redis connection
        self.redis_host = REDIS_HOST
        self.redis_port = REDIS_MASTER_PORT
        self.redis_db = REDIS_DB
        self.redis_password = REDIS_PASSWORD

        # Connection pooling settings
        self.connection_pool_size = REDIS_CONNECTION_POOL_SIZE
        self.socket_timeout = REDIS_SOCKET_TIMEOUT
        self.socket_connect_timeout = REDIS_SOCKET_CONNECT_TIMEOUT

        self._connection: Optional[Redis] = None
        self._read_connection: Optional[Redis] = None
        self._sentinel: Optional[Sentinel] = None
        self._batch_queue: Optional[Queue] = None
        self._report_generation_queue: Optional[Queue] = None

    @property
    def connection(self) -> Redis:
        """Get or create Redis connection with Sentinel support."""
        if self._connection is None:
            if self.use_sentinel:
                self._connection = self._create_sentinel_connection()
            else:
                self._connection = self._create_direct_connection()
        return self._connection

    def _create_sentinel_connection(self) -> Redis:
        """Create Redis connection through Sentinel."""
        try:
            if self._sentinel is None:
                self._sentinel = Sentinel(
                    self.sentinel_hosts,
                    socket_timeout=self.socket_timeout,
                    socket_connect_timeout=self.socket_connect_timeout,
                    password=self.redis_password,
                )

            # Get master connection
            master = self._sentinel.master_for(
                self.sentinel_service_name,
                socket_timeout=self.socket_timeout,
                socket_connect_timeout=self.socket_connect_timeout,
                db=self.redis_db,
                password=self.redis_password,
                retry_on_timeout=True,
                health_check_interval=30,
            )

            logger.info(
                f"Connected to Redis master via Sentinel: {self.sentinel_service_name}"
            )
            return master

        except Exception as e:
            logger.error(f"Failed to connect via Sentinel: {e}")
            logger.info("Falling back to direct Redis connection")
            return self._create_direct_connection()

    def _create_direct_connection(self) -> Redis:
        """Create direct Redis connection (fallback)."""
        connection = Redis(
            host=self.redis_host,
            port=self.redis_port,
            db=self.redis_db,
            password=self.redis_password,
            socket_timeout=self.socket_timeout,
            socket_connect_timeout=self.socket_connect_timeout,
            retry_on_timeout=True,
            health_check_interval=30,
        )
        logger.info(f"Connected to Redis directly: {self.redis_host}:{self.redis_port}")
        return connection

    @property
    def read_connection(self) -> Redis:
        """Get or create Redis read connection (preferably to replica)."""
        if self._read_connection is None:
            if self.use_sentinel:
                self._read_connection = self._create_sentinel_read_connection()
            else:
                # Fallback to master connection for reads
                self._read_connection = self._create_direct_connection()
        return self._read_connection

    def _create_sentinel_read_connection(self) -> Redis:
        """Create Redis read connection through Sentinel (prefers replica)."""
        try:
            if self._sentinel is None:
                self._sentinel = Sentinel(
                    self.sentinel_hosts,
                    socket_timeout=self.socket_timeout,
                    socket_connect_timeout=self.socket_connect_timeout,
                    password=self.redis_password,
                )

            # Try to get replica connection first for read operations
            try:
                replica = self._sentinel.slave_for(
                    self.sentinel_service_name,
                    socket_timeout=self.socket_timeout,
                    socket_connect_timeout=self.socket_connect_timeout,
                    db=self.redis_db,
                    password=self.redis_password,
                    retry_on_timeout=True,
                    health_check_interval=30,
                )
                logger.info(
                    f"Connected to Redis replica via Sentinel: {self.sentinel_service_name}"
                )
                return replica
            except Exception as replica_error:
                logger.warning(
                    f"Failed to connect to replica, falling back to master: {replica_error}"
                )
                # Fall back to master for reads if replica unavailable
                master = self._sentinel.master_for(
                    self.sentinel_service_name,
                    socket_timeout=self.socket_timeout,
                    socket_connect_timeout=self.socket_connect_timeout,
                    db=self.redis_db,
                    password=self.redis_password,
                    retry_on_timeout=True,
                    health_check_interval=30,
                )
                logger.info(
                    f"Using Redis master for reads via Sentinel: {self.sentinel_service_name}"
                )
                return master

        except Exception as e:
            logger.error(f"Failed to connect via Sentinel for reads: {e}")
            logger.info("Falling back to direct Redis connection for reads")
            return self._create_direct_connection()

    @property
    def batch_queue(self) -> Queue:
        """Get or create batch processing queue."""
        if self._batch_queue is None:
            self._batch_queue = Queue(
                "batch_processing",
                connection=self.connection,
                default_timeout=1800,
            )
        return self._batch_queue

    @property
    def report_generation_queue(self) -> Queue:
        """Get or create report generation queue."""
        if self._report_generation_queue is None:
            self._report_generation_queue = Queue(
                "report_generation",
                connection=self.connection,
                default_timeout=3600,
            )
        return self._report_generation_queue

    def test_connection(self) -> bool:
        """Test Redis connection."""
        try:
            self.connection.ping()
            logger.info("Redis connection successful")
            return True
        except Exception as e:
            logger.error(f"Redis connection failed: {e}")
            return False

    def get_sentinel_info(self) -> Dict[str, Any]:
        """Get Redis Sentinel information and status."""
        if not self.use_sentinel or self._sentinel is None:
            return {"sentinel_enabled": False, "message": "Sentinel not configured"}

        try:
            sentinel_info = {}
            for i, (host, port) in enumerate(self.sentinel_hosts):
                try:
                    sentinel_conn = self._sentinel.sentinel_kwargs.get(
                        "connection_pool", None
                    )
                    if sentinel_conn:
                        sentinel_info[f"sentinel_{i+1}"] = {
                            "host": host,
                            "port": port,
                            "status": "connected",
                        }
                except:
                    sentinel_info[f"sentinel_{i+1}"] = {
                        "host": host,
                        "port": port,
                        "status": "disconnected",
                    }

            # Get master info
            try:
                master_info = self._sentinel.discover_master(self.sentinel_service_name)
                sentinel_info["master"] = {
                    "host": master_info[0],
                    "port": master_info[1],
                    "service_name": self.sentinel_service_name,
                }
            except Exception as e:
                sentinel_info["master"] = {"error": str(e)}

            # Get replica info
            try:
                replicas = self._sentinel.discover_slaves(self.sentinel_service_name)
                sentinel_info["replicas"] = [
                    {"host": replica[0], "port": replica[1]} for replica in replicas
                ]
            except Exception as e:
                sentinel_info["replicas"] = {"error": str(e)}

            return {
                "sentinel_enabled": True,
                "service_name": self.sentinel_service_name,
                "sentinels": sentinel_info,
            }

        except Exception as e:
            logger.error(f"Failed to get Sentinel info: {e}")
            return {"sentinel_enabled": True, "error": str(e)}

    def reset_connection(self):
        """Reset Redis connections (useful during failover)."""
        if self._connection:
            try:
                self._connection.close()
            except:
                pass
            self._connection = None

        if self._read_connection:
            try:
                self._read_connection.close()
            except:
                pass
            self._read_connection = None

        if self._sentinel:
            try:
                # Reset sentinel connection
                self._sentinel = None
            except:
                pass

        logger.info("Redis connections reset - will reconnect on next access")

    def get_redis_info(self) -> Dict[str, Any]:
        """Get Redis server information."""
        try:
            # Basic connection info without detailed server stats
            self.connection.ping()  # Test connection
            return {
                "connection_status": "healthy",
                "sentinel_enabled": self.use_sentinel,
                "redis_host": self.redis_host,
                "redis_port": self.redis_port,
            }
        except Exception as e:
            logger.error(f"Failed to get Redis info: {e}")
            return {"error": str(e), "connection_status": "failed"}

    def get_replication_info(self) -> Dict[str, Any]:
        """Get Redis replication information and lag metrics."""
        try:
            replication_info = {
                "timestamp": datetime.utcnow().isoformat(),
                "master_connection_status": "unknown",
                "replica_connection_status": "unknown",
                "sentinel_enabled": self.use_sentinel,
                "replication_health": "unknown",
            }

            # Test master connection
            try:
                self.connection.ping()
                replication_info["master_connection_status"] = "healthy"
            except Exception as e:
                replication_info["master_connection_status"] = f"failed: {str(e)}"

            # Test replica connection if available
            if self.use_sentinel:
                try:
                    self.read_connection.ping()
                    replication_info["replica_connection_status"] = "healthy"
                    replication_info["replication_health"] = "good"
                except Exception as e:
                    replication_info["replica_connection_status"] = f"failed: {str(e)}"
                    replication_info["replication_health"] = "degraded"

            return replication_info

        except Exception as e:
            logger.error(f"Failed to get replication info: {e}")
            return {"error": str(e), "timestamp": datetime.utcnow().isoformat()}

    def get_connection_pool_stats(self) -> Dict[str, Any]:
        """Get connection pool statistics."""
        try:
            stats = {
                "timestamp": datetime.utcnow().isoformat(),
                "master_connection": {},
                "read_connection": {},
                "sentinel_enabled": self.use_sentinel,
            }

            # Master connection stats
            if self._connection and hasattr(self._connection, "connection_pool"):
                pool = self._connection.connection_pool
                stats["master_connection"] = {
                    "created_connections": getattr(pool, "created_connections", 0),
                    "available_connections": len(
                        getattr(pool, "_available_connections", [])
                    ),
                    "in_use_connections": len(getattr(pool, "_in_use_connections", [])),
                }

            # Read connection stats
            if self._read_connection and hasattr(
                self._read_connection, "connection_pool"
            ):
                pool = self._read_connection.connection_pool
                stats["read_connection"] = {
                    "created_connections": getattr(pool, "created_connections", 0),
                    "available_connections": len(
                        getattr(pool, "_available_connections", [])
                    ),
                    "in_use_connections": len(getattr(pool, "_in_use_connections", [])),
                }

            return stats

        except Exception as e:
            logger.error(f"Failed to get connection pool stats: {e}")
            return {"error": str(e), "timestamp": datetime.utcnow().isoformat()}

    def get_queue_stats(self) -> Dict[str, Any]:
        """Get statistics for all queues."""
        return {
            "batch_processing": {
                "queued": len(self.batch_queue),
                "failed": self.batch_queue.failed_job_registry.count,
                "finished": self.batch_queue.finished_job_registry.count,
                "started": self.batch_queue.started_job_registry.count,
            },
            "report_generation": {
                "queued": len(self.report_generation_queue),
                "failed": self.report_generation_queue.failed_job_registry.count,
                "finished": self.report_generation_queue.finished_job_registry.count,
                "started": self.report_generation_queue.started_job_registry.count,
            },
        }

    def get_detailed_metrics(self) -> Dict[str, Any]:
        """Get detailed metrics including processing times and queue health."""
        try:
            metrics = {
                "timestamp": datetime.utcnow().isoformat(),
                "queues": self.get_queue_stats(),
                "processing_times": self._get_processing_time_metrics(),
                "queue_health": self._assess_queue_health(),
            }
            return metrics
        except Exception as e:
            logger.error(f"Failed to get detailed metrics: {e}")
            return {"error": str(e), "timestamp": datetime.utcnow().isoformat()}

    def _get_processing_time_metrics(self) -> Dict[str, Any]:
        """Calculate processing time metrics from recent jobs."""
        try:
            batch_times = []
            report_generation_times = []

            # Get recent finished jobs from batch queue
            for job in self.batch_queue.finished_job_registry.get_job_ids()[
                -50:
            ]:  # Last 50 jobs
                try:
                    job_obj = self.batch_queue.finished_job_registry.requeue(job)
                    if (
                        job_obj
                        and hasattr(job_obj, "meta")
                        and "processing_duration" in job_obj.meta
                    ):
                        batch_times.append(job_obj.meta["processing_duration"])
                except:
                    continue

            # Get recent finished jobs from report_generation queue
            for job in self.report_generation_queue.finished_job_registry.get_job_ids()[
                -20:
            ]:  # Last 20 jobs
                try:
                    job_obj = (
                        self.report_generation_queue.finished_job_registry.requeue(job)
                    )
                    if (
                        job_obj
                        and hasattr(job_obj, "meta")
                        and "processing_duration" in job_obj.meta
                    ):
                        report_generation_times.append(
                            job_obj.meta["processing_duration"]
                        )
                except:
                    continue

            return {
                "batch_processing": {
                    "count": len(batch_times),
                    "avg_time": (
                        sum(batch_times) / len(batch_times) if batch_times else 0
                    ),
                    "min_time": min(batch_times) if batch_times else 0,
                    "max_time": max(batch_times) if batch_times else 0,
                },
                "report_generation": {
                    "count": len(report_generation_times),
                    "avg_time": (
                        sum(report_generation_times) / len(report_generation_times)
                        if report_generation_times
                        else 0
                    ),
                    "min_time": (
                        min(report_generation_times) if report_generation_times else 0
                    ),
                    "max_time": (
                        max(report_generation_times) if report_generation_times else 0
                    ),
                },
            }
        except Exception as e:
            logger.error(f"Failed to calculate processing time metrics: {e}")
            return {"error": str(e)}

    def _assess_individual_queue_health(self, stats: dict, queue_name: str) -> str:
        """Assess health of an individual queue.

        Args:
            stats: Dictionary containing queue statistics.
            queue_name: The name of the queue to assess.

        Returns:
            str: Health status ('healthy', 'degraded', 'overloaded')
        """
        queued = stats[queue_name]["queued"]
        failed = stats[queue_name]["failed"]

        max_queued = 100  # Default for batch_processing
        if queue_name == "report_generation":
            max_queued = 50

        if queued > max_queued:
            return "overloaded"
        elif failed > queued * 0.1:  # More than 10% failure rate
            return "degraded"
        else:
            return "healthy"

    def _assess_queue_health(self) -> Dict[str, str]:
        """Assess overall queue health."""
        try:
            stats = self.get_queue_stats()
            health = {}

            health["batch_processing"] = self._assess_individual_queue_health(
                stats, "batch_processing"
            )

            health["report_generation"] = self._assess_individual_queue_health(
                stats, "report_generation"
            )

            if any(status in ["overloaded", "degraded"] for status in health.values()):
                health["overall"] = (
                    "degraded" if "degraded" in health.values() else "overloaded"
                )
            else:
                health["overall"] = "healthy"

            return health

        except Exception as e:
            logger.error(f"Failed to assess queue health: {e}")
            return {"overall": "unknown", "error": str(e)}

    def record_job_metrics(
        self, job_id: str, job_type: str, processing_time: float, success: bool
    ):
        """Record job metrics for monitoring."""
        try:
            metrics_key = f"job_metrics:{job_id}:{job_type}:{datetime.now(timezone.utc).strftime('%Y-%m-%d:%H')}"

            self.connection.hincrby(metrics_key, "total_jobs", 1)
            self.connection.hincrby(
                metrics_key, "total_time", int(processing_time * 1000)
            )  # Store in ms

            if success:
                self.connection.hincrby(metrics_key, "successful_jobs", 1)
            else:
                self.connection.hincrby(metrics_key, "failed_jobs", 1)

            # Set expiration for metrics (keep for 7 days)
            self.connection.expire(metrics_key, 7 * 24 * 3600)

        except Exception as e:
            logger.error(f"Failed to record job metrics: {e}")

    def create_worker(self, queues: Optional[list] = None) -> Worker:
        """Create an RQ worker for the specified queues."""
        if queues is None:
            queues = [self.batch_queue, self.report_generation_queue]

        hostname = os.getenv("HOSTNAME", "unknown-host")
        unique_id = str(uuid.uuid4())[:8]
        worker_name = f"worker-{hostname}-{unique_id}"

        return Worker(
            queues,
            connection=self.connection,
            name=worker_name,
            exception_handlers=[self._handle_worker_exception],
            default_worker_ttl=3600,  # Worker TTL: 1 hour
            default_result_ttl=86400,  # Result TTL: 24 hours
        )

    def _handle_worker_exception(self, job, exc_type, exc_value, traceback):
        """Handle worker exceptions with enhanced logging for Kibana analysis."""
        import json
        import traceback as tb
        from datetime import datetime, timezone

        # Enhanced structured logging for failed jobs (for Kibana)
        error_context = {
            "job_id": job.id,
            "function_name": job.func_name,
            "error_type": exc_type.__name__ if exc_type else "Unknown",
            "error_message": str(exc_value),
            "failed_at": datetime.now(timezone.utc).isoformat(),
            "args": json.dumps(getattr(job, "args", []), default=str),
            "kwargs": json.dumps(getattr(job, "kwargs", {}), default=str),
            "origin_queue": getattr(job, "origin", "unknown"),
            "created_at": (
                job.created_at.isoformat()
                if hasattr(job, "created_at") and job.created_at
                else None
            ),
            "enqueued_at": (
                job.enqueued_at.isoformat()
                if hasattr(job, "enqueued_at") and job.enqueued_at
                else None
            ),
            "started_at": (
                job.started_at.isoformat()
                if hasattr(job, "started_at") and job.started_at
                else None
            ),
            "retry_count": str(getattr(job, "retries_left", 0)),
            "worker_name": str(getattr(job, "worker_name", None)),
            "traceback": (
                "".join(tb.format_exception(exc_type, exc_value, traceback))
                if traceback
                else None
            ),
        }

        # Enhanced structured logging for Kibana analysis
        logger.error(
            f"Worker exception in job {job.id} ({job.func_name}): {exc_value}",
            extra={"job_failure_context": error_context},
        )

        # Record failure metrics
        self.record_job_metrics(job.id, job.func_name, 0, False)

    def _job_to_dict(self, job) -> dict:
        """Convert an RQ Job object to dictionary format expected by the API."""
        return {
            "job_id": job.id,
            "function_name": job.func_name,
            "error_type": "Unknown",  # RQ doesn't store error type
            "error_message": job.exc_info or "Job failed",
            "failed_at": job.ended_at.isoformat() if job.ended_at else "",
            "args": str(job.args) if hasattr(job, "args") else "[]",
            "kwargs": str(job.kwargs) if hasattr(job, "kwargs") else "{}",
            "origin_queue": job.origin or "unknown",
            "created_at": job.created_at.isoformat() if job.created_at else "",
            "enqueued_at": job.enqueued_at.isoformat() if job.enqueued_at else "",
            "started_at": job.started_at.isoformat() if job.started_at else "",
            "retry_count": getattr(job, "retries_left", 0) or 0,
            "worker_name": getattr(job, "worker_name", "unknown") or "unknown",
            "traceback": job.exc_info or "",
        }

    def _get_all_failed_job_ids(self) -> list:
        """Get all failed job IDs from both batch and report_generation queues."""
        batch_failed_jobs = self.batch_queue.failed_job_registry.get_job_ids()
        report_generation_failed_jobs = (
            self.report_generation_queue.failed_job_registry.get_job_ids()
        )
        return list(batch_failed_jobs) + list(report_generation_failed_jobs)

    def _execute_on_failed_registries(
        self, job_id: str, operation: str
    ) -> tuple[bool, str]:
        """Execute an operation on failed job registries, trying batch first then report_generation.

        Args:
            job_id: The job ID to operate on
            operation: The operation name ('requeue', 'remove', etc.)

        Returns:
            tuple: (success: bool, queue_name: str)
        """
        queue_configs = [
            (self.batch_queue, "batch_processing"),
            (self.report_generation_queue, "report_generation"),
        ]

        for queue, queue_name in queue_configs:
            if job_id in queue.failed_job_registry.get_job_ids():
                try:
                    getattr(queue.failed_job_registry, operation)(job_id)
                    return True, queue_name
                except Exception as e:
                    logger.error(
                        f"Failed to {operation} job {job_id} from {queue_name}: {e}"
                    )

        return False, ""

    def get_dlq_jobs(self, limit: int = 50, offset: int = 0) -> list:
        """Get failed jobs from RQ's built-in failed job registry with pagination."""
        try:
            from rq.job import Job

            jobs = []

            # Get failed jobs from both queues
            all_failed_job_ids = self._get_all_failed_job_ids()

            logger.info(
                f"Found {len(all_failed_job_ids)} failed job IDs: {all_failed_job_ids}"
            )

            # Apply pagination
            paginated_job_ids = all_failed_job_ids[offset : offset + limit]

            logger.info(
                f"Processing {len(paginated_job_ids)} paginated job IDs: {paginated_job_ids}"
            )

            for job_id in paginated_job_ids:
                try:
                    job = Job.fetch(job_id, connection=self.connection)

                    if job:
                        logger.info(
                            f"Successfully fetched job {job_id}: {job.func_name}"
                        )
                        # Convert job to dict format expected by API
                        job_dict = self._job_to_dict(job)
                        jobs.append(job_dict)
                    else:
                        logger.warning(f"Job.fetch returned None for job_id {job_id}")
                except Exception as job_error:
                    logger.error(f"Failed to process job {job_id}: {job_error}")
                    continue

            logger.info(f"Returning {len(jobs)} jobs")
            return jobs
        except Exception as e:
            logger.error(f"Failed to get failed jobs: {e}")
            return []

    def get_dlq_job_details(self, job_id: str) -> dict:
        """Get detailed information about a specific failed job from RQ's failed job registry."""
        try:
            from rq.job import Job

            job = Job.fetch(job_id, connection=self.connection)

            if job:
                return self._job_to_dict(job)
            return {}
        except Exception as e:
            logger.error(f"Failed to get failed job details for {job_id}: {e}")
            return {}

    def get_dlq_stats(self) -> dict:
        """Get basic failed job statistics from RQ's built-in registries."""
        try:
            batch_failed_count = self.batch_queue.failed_job_registry.count
            report_generation_failed_count = (
                self.report_generation_queue.failed_job_registry.count
            )
            total_failed_jobs = batch_failed_count + report_generation_failed_count

            return {
                "total_failed_jobs": total_failed_jobs,
                "batch_processing_failed": batch_failed_count,
                "report_generation_failed": report_generation_failed_count,
            }
        except Exception as e:
            logger.error(f"Failed to get failed job stats: {e}")
            return {
                "total_failed_jobs": 0,
                "batch_processing_failed": 0,
                "report_generation_failed": 0,
            }

    def requeue_dlq_job(self, job_id: str) -> bool:
        """Requeue a failed job from RQ's built-in failed job registry."""
        try:
            success, queue_name = self._execute_on_failed_registries(job_id, "requeue")
            if success:
                logger.info(
                    f"Successfully requeued job {job_id} from {queue_name} failed registry"
                )
                return True
            else:
                logger.error(f"Failed job {job_id} not found in any failed registry")
                return False
        except Exception as e:
            logger.error(f"Failed to requeue job {job_id}: {e}")
            return False

    def bulk_requeue_dlq_jobs(self, job_ids: list) -> dict:
        """Bulk requeue multiple failed jobs from RQ's built-in failed job registries."""
        results = {"success": [], "failed": []}

        for job_id in job_ids:
            try:
                if self.requeue_dlq_job(job_id):
                    results["success"].append(job_id)
                else:
                    results["failed"].append(job_id)
            except Exception as e:
                logger.error(f"Failed to requeue job {job_id}: {e}")
                results["failed"].append(job_id)

        return results

    def delete_dlq_job(self, job_id: str) -> bool:
        """Delete a failed job from RQ's built-in failed job registry."""
        try:
            success, queue_name = self._execute_on_failed_registries(job_id, "remove")
            if success:
                logger.info(
                    f"Successfully deleted job {job_id} from {queue_name} failed registry"
                )
                return True
            else:
                logger.error(f"Failed job {job_id} not found in any failed registry")
                return False
        except Exception as e:
            logger.error(f"Failed to delete job {job_id}: {e}")
            return False

    def clear_dlq_job(self, job_id: str) -> bool:
        """Clear a failed job from RQ's built-in failed job registry (alias for delete_dlq_job)."""
        return self.delete_dlq_job(job_id)

    def cleanup_finished_jobs(self, max_age_hours: int = 24) -> Dict[str, int]:
        """Clean up finished jobs older than max_age_hours."""
        cutoff_time = datetime.now(timezone.utc).timestamp() - (max_age_hours * 3600)
        cleaned = {"batch_processing": 0, "report_generation": 0}

        # Clean batch processing queue
        try:
            for job in self.batch_queue.finished_job_registry.get_job_ids()[
                :100
            ]:  # Process in chunks
                job_obj = self.batch_queue.finished_job_registry.requeue(job)
                if (
                    job_obj
                    and job_obj.ended_at
                    and job_obj.ended_at.timestamp() < cutoff_time
                ):
                    self.batch_queue.finished_job_registry.remove(job)
                    cleaned["batch_processing"] += 1
        except Exception as e:
            logger.error(f"Error cleaning batch_processing finished jobs: {e}")

        # Clean report generation queue
        try:
            for job in self.report_generation_queue.finished_job_registry.get_job_ids()[
                :100
            ]:  # Process in chunks
                job_obj = self.report_generation_queue.finished_job_registry.requeue(
                    job
                )
                if (
                    job_obj
                    and job_obj.ended_at
                    and job_obj.ended_at.timestamp() < cutoff_time
                ):
                    self.report_generation_queue.finished_job_registry.remove(job)
                    cleaned["report_generation"] += 1
        except Exception as e:
            logger.error(f"Error cleaning report_generation finished jobs: {e}")

        return cleaned

    def get_failed_jobs_count(self) -> int:
        """Get total count of failed jobs across all queues."""
        return (
            self.batch_queue.failed_job_registry.count
            + self.report_generation_queue.failed_job_registry.count
        )


def log_failed_job(job_id: str, error_message: str):
    """Log failed job details for debugging."""
    logger.error(f"Failed job {job_id}: {error_message}")


# Global queue configuration instance
queue_config = QueueConfig()
