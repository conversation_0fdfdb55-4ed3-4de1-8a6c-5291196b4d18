from datetime import datetime
from typing import Any, Dict, Optional

from pydantic import BaseModel, Field


class DLQJobInfo(BaseModel):
    """Information about a failed job in the Dead Letter Queue."""

    job_id: str = Field(..., description="RQ job ID")
    original_queue: str = Field(..., description="Original queue name")
    function_name: str = Field(..., description="Function that failed")
    args: list = Field(default_factory=list, description="Job arguments")
    kwargs: Dict[str, Any] = Field(
        default_factory=dict, description="Job keyword arguments"
    )

    # Error information
    error_message: str = Field(..., description="Error message")
    error_type: str = Field(..., description="Exception type")
    traceback: Optional[str] = Field(None, description="Full traceback")

    # Timing information
    created_at: datetime = Field(..., description="When job was originally created")
    failed_at: datetime = Field(..., description="When job failed")
    enqueued_at: Optional[datetime] = Field(None, description="When job was enqueued")
    started_at: Optional[datetime] = Field(
        None, description="When job started processing"
    )

    # Retry information
    retry_count: int = Field(default=0, description="Number of retries attempted")
    max_retries: int = Field(default=3, description="Maximum retries allowed")

    # Metadata
    worker_name: Optional[str] = Field(
        None, description="Worker that processed the job"
    )
    metadata: Dict[str, Any] = Field(
        default_factory=dict, description="Additional metadata"
    )


class DLQJobResponse(BaseModel):
    """Response model for DLQ job information."""

    job_info: DLQJobInfo
    can_requeue: bool = Field(..., description="Whether job can be requeued")
    requeue_reason: Optional[str] = Field(
        None, description="Reason why job cannot be requeued"
    )


class DLQStatsResponse(BaseModel):
    """Response model for DLQ statistics."""

    total_failed_jobs: int = Field(..., description="Total jobs in DLQ")
    batch_processing_failed: int = Field(
        ..., description="Failed jobs in batch processing queue"
    )
    report_generation_failed: int = Field(
        ..., description="Failed jobs in report generation queue"
    )
