"""
Batch Processing Job

Converts the visit batch audio processing functionality to a class-based job.
"""

import asyncio
import logging
import os
import tempfile
from datetime import datetime, timezone
from typing import List

from pydub import AudioSegment

from acva_ai.database.mongo import mongo_instance
from acva_ai.models.visit_job import BatchStatus
from acva_ai.services.base_job import BaseJob, register_job
from acva_ai.services.minio_client import MinioClient
from acva_ai.services.thread_pool_service import get_thread_pool_service
from acva_ai.utils.audio_utils import normalize_audio_segment

logger = logging.getLogger(__name__)


@register_job
class ProcessVisitBatchAudioJob(BaseJob):
    """Job for processing individual audio batches within visit jobs."""

    name = "Process Visit Batch Audio"
    description = "Process a batch of audio files for a visit job"
    timeout = 1800  # 30 minutes
    queue_name = "batch_processing"
    max_retries = 3
    retry_delay = 120

    def __init__(self):
        super().__init__()
        self.minio_client = MinioClient(bucket_name="acva-visit-results")
        self.raw_audio_minio_client = MinioClient(bucket_name="acva-audio")
        self.thread_pool_service = get_thread_pool_service()

        # Needed to avoid circular import
        from acva_ai.services.visit_job_coordinator import VisitJobCoordinator

        self.visit_coordinator = VisitJobCoordinator()

    def execute(self, batch_id: str, minio_paths: List[str]) -> str:
        """Process a batch of audio files for a visit job."""
        try:
            batch_data = mongo_instance.get_batch(batch_id)
            if not batch_data:
                raise ValueError(f"Batch {batch_id} not found")

            visit_job_id = str(batch_data["visit_job_id"])
            batch_number = batch_data["batch_number"]

            self.logger.info(
                f"Processing batch {batch_number} for visit job {visit_job_id} with {len(minio_paths)} audio files"
            )

            self._update_job_progress(
                10,
                f"Starting batch {batch_number} processing ({len(minio_paths)} files)",
            )
            self.visit_coordinator.update_batch_status(batch_id, BatchStatus.PROCESSING)

            self._update_job_progress(20, "Processing audio files")
            processed_audio_path = self._process_audio_batch(
                batch_id, minio_paths, batch_number
            )

            processing_duration = (
                datetime.now(timezone.utc) - self.start_time
            ).total_seconds()

            self._update_job_progress(90, "Updating batch status")
            self.visit_coordinator.update_batch_status(
                batch_id,
                BatchStatus.COMPLETED,
                processed_audio_path=processed_audio_path,
                processing_duration=processing_duration,
            )

            try:
                self.raw_audio_minio_client.cleanup_raw_audio_files(
                    visit_job_id, batch_number
                )
            except Exception as cleanup_error:
                self.logger.warning(
                    f"Failed to cleanup raw audio files for batch {batch_id}: {cleanup_error}"
                )

            self.logger.info(
                f"Successfully processed batch {batch_number} for visit job {visit_job_id} in {processing_duration:.2f}s"
            )
            return processed_audio_path

        except Exception as e:
            processing_duration = (
                datetime.now(timezone.utc) - self.start_time
            ).total_seconds()

            error_msg = f"Error processing batch {batch_id}: {str(e)}"
            self.logger.error(error_msg)

            self.visit_coordinator.update_batch_status(
                batch_id,
                BatchStatus.FAILED,
                error_message=str(e),
                processing_duration=processing_duration,
            )
            raise

    def _process_audio_batch(
        self, batch_id: str, minio_paths: List[str], batch_number: int
    ) -> str:
        """Process the audio files in a batch with parallel downloading."""
        return asyncio.run(
            self._process_audio_batch_async(batch_id, minio_paths, batch_number)
        )

    async def _process_audio_batch_async(
        self, batch_id: str, minio_paths: List[str], batch_number: int
    ) -> str:
        """Async implementation of batch processing with parallel downloads."""
        try:
            self.logger.info(
                f"Starting batch processing for batch {batch_id} with {len(minio_paths)} files"
            )

            temp_dir = os.path.join(
                tempfile.gettempdir(), "acva_ai", "visit_batch_processing"
            )
            os.makedirs(temp_dir, exist_ok=True)
            self.logger.debug(f"Created temp directory: {temp_dir}")

            self._update_job_progress(30, f"Downloading {len(minio_paths)} audio files")

            def download_and_process_file(i: int, minio_path: str):
                """Download and process a single audio file."""
                try:
                    self.logger.debug(
                        f"Processing file {i+1}/{len(minio_paths)}: {minio_path}"
                    )

                    audio_data = self.raw_audio_minio_client.download_file(minio_path)
                    self.logger.debug(
                        f"Downloaded {len(audio_data)} bytes from {minio_path}"
                    )

                    file_name = os.path.basename(minio_path)
                    file_extension = (
                        file_name.split(".")[-1] if "." in file_name else "wav"
                    )
                    self.logger.debug(f"File extension: {file_extension}")

                    temp_file_path = os.path.join(
                        temp_dir, f"downloaded_{i}_{os.path.basename(minio_path)}"
                    )

                    with open(temp_file_path, "wb") as f:
                        f.write(audio_data)

                    audio_segment = AudioSegment.from_file(
                        temp_file_path, format=file_extension
                    )

                    os.remove(temp_file_path)

                    self.logger.debug(f"Processed file {i+1}: {len(audio_segment)}ms")
                    return i, audio_segment

                except Exception as e:
                    self.logger.error(f"Failed to process file {minio_path}: {e}")
                    raise ValueError(f"Failed to process audio file {minio_path}: {e}")

            args_list = [(i, minio_path) for i, minio_path in enumerate(minio_paths)]
            results = await self.thread_pool_service.gather_with_asyncio_to_thread(
                download_and_process_file, args_list
            )

            # Sort by original index of the files in minio_paths
            results.sort(key=lambda x: x[0])
            audio_segments = [segment for _, segment in results]

            if not audio_segments:
                raise ValueError("No valid audio segments found for processing")

            self._update_job_progress(
                60, f"Concatenating {len(audio_segments)} audio segments"
            )
            self.logger.info(f"Concatenating {len(audio_segments)} audio segments")
            combined_audio = audio_segments[0]
            for segment in audio_segments[1:]:
                combined_audio += segment

            self._update_job_progress(70, "Normalizing combined audio")
            self.logger.info("Normalizing combined audio")
            normalized_audio = normalize_audio_segment(combined_audio)

            self._update_job_progress(80, "Uploading processed audio to storage")
            temp_output_path = os.path.join(temp_dir, f"processed_batch_{batch_id}.wav")
            normalized_audio.export(temp_output_path, format="wav")

            object_name = f"batch_results/{batch_id}/processed_audio.wav"
            with open(temp_output_path, "rb") as f:
                file_size = os.path.getsize(temp_output_path)
                self.minio_client.upload_file(
                    object_name, f, file_size, content_type="audio/wav"
                )

            os.remove(temp_output_path)

            self.logger.info(
                f"Successfully processed batch {batch_number}: {len(normalized_audio)}ms of audio"
            )
            return object_name

        except Exception as e:
            self.logger.error(f"Failed to process audio batch {batch_id}: {e}")
            raise


def ProcessVisitBatchAudioJob_rq_job(*args, **kwargs):
    """RQ job function for ProcessVisitBatchAudioJob."""
    job_instance = ProcessVisitBatchAudioJob()
    return job_instance.run(*args, **kwargs)
