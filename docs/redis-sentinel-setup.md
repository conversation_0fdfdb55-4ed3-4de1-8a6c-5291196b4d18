# Redis Sentinel High Availability Setup

This document describes the Redis Sentinel configuration for the ACVA AI project, providing high availability and automatic failover for the Redis infrastructure.

## Overview

The Redis Sentinel setup provides:

- **High Availability**: Automatic failover from master to replica
- **Master Discovery**: Automatic discovery of the current Redis master
- **Connection Resilience**: Applications automatically reconnect after failover
- **Data Persistence**: Both RDB and AOF persistence enabled
- **Monitoring**: Health checks and monitoring endpoints

## Architecture

```
┌─────────────────┐    ┌─────────────────┐
│   Sentinel-1    │    │   Sentinel-2    │
│   (26379)       │    │   (26380)       │
└─────────────────┘    └─────────────────┘
         │                       │
         └───────────┬───────────┘
                     │
         ┌─────────────────┐
         │  Redis Master   │
         │    (6379)       │
         └─────────────────┘
                     │
         ┌─────────────────┐
         │ Redis Replica   │
         │    (6380)       │
         └─────────────────┘
```

## Configuration Files

### Redis Master (`redis-configs/redis-master.conf`)

- **Port**: 6379
- **Persistence**: RDB + AOF enabled
- **Memory**: 512MB with LRU eviction
- **Replication**: Configured as master

### Redis Replica (`redis-configs/redis-replica.conf`)

- **Port**: 6379 (exposed as 6380)
- **Persistence**: RDB + AOF enabled
- **Replication**: Follows redis-master
- **Read-only**: Yes

### Sentinel Configuration

- **Service Name**: `acva-redis`
- **Quorum**: 1 (configured for minimum availability with two sentinels)
- **Down After**: 30 seconds
- **Failover Timeout**: 180 seconds
- **Parallel Syncs**: 1

## Environment Variables

To enable Sentinel mode, set these environment variables:

```bash
# Enable Redis Sentinel
REDIS_USE_SENTINEL=true

# Sentinel service name
REDIS_SENTINEL_SERVICE_NAME=acva-redis

# Sentinel hosts (comma-separated)
REDIS_SENTINEL_HOSTS=sentinel-1:26379,sentinel-2:26379,sentinel-3:26379

# Connection settings
REDIS_CONNECTION_POOL_SIZE=10
REDIS_SOCKET_TIMEOUT=5
REDIS_SOCKET_CONNECT_TIMEOUT=5
```

## Deployment

### Docker Compose Setup

The setup includes:

- `redis-master`: Primary Redis instance
- `redis-replica`: Replica Redis instance
- `sentinel-1`: First Sentinel node
- `sentinel-2`: Second Sentinel node

### Starting the Services

```bash
# Start all Redis services
docker compose -f docker-compose.local.yml up -d redis-master redis-replica sentinel-1 sentinel-2

# Verify services are running
docker compose -f docker-compose.local.yml ps

# Check logs
docker compose -f docker-compose.local.yml logs -f sentinel-1
docker compose -f docker-compose.local.yml logs -f sentinel-2
```

### Health Checks

All services include health checks:

- Redis instances: `redis-cli ping`
- Sentinels: Built-in health monitoring

## Application Integration

### QueueConfig Changes

The `QueueConfig` class automatically detects Sentinel mode and:

1. Connects to Sentinel nodes
2. Discovers the current master
3. Falls back to direct connection if Sentinel fails
4. Provides connection reset functionality

### Connection Flow

1. **Sentinel Mode**: Connect to Sentinel → Discover master → Connect to master
2. **Fallback Mode**: Direct connection to configured Redis host
3. **Failover**: Sentinel promotes replica → Application reconnects automatically

## Monitoring and Management

### API Endpoints

New monitoring endpoints are available:

```bash
# Get Sentinel information
GET /api/v1/dlq/redis/sentinel-info

# Get Redis connection status
GET /api/v1/dlq/redis/info

# Reset Redis connection (force reconnect)
POST /api/v1/dlq/redis/reset-connection
```

### Sentinel Commands

Connect to Sentinel to check status:

```bash
# Connect to Sentinel
docker exec -it sentinel-1 redis-cli -p 26379

# Check master status
SENTINEL masters

# Check replica status
SENTINEL slaves acva-redis

# Check Sentinel status
SENTINEL sentinels acva-redis
```

### Redis Commands

Check replication status:

```bash
# Connect to master
docker exec -it redis-master redis-cli

# Check replication info
INFO replication

# Connect to replica
docker exec -it redis-replica redis-cli

# Verify replica status
INFO replication
```

## Failover Testing

### Manual Failover

Test failover by stopping the master:

```bash
# Stop Redis master
docker compose -f docker-compose.local.yml stop redis-master

# Check Sentinel logs - should promote replica
docker compose -f docker-compose.local.yml logs -f sentinel-1

# Verify application still works
curl -X GET "http://localhost:8000/api/v1/dlq/redis/info" \
  -H "X-API-Key: YOUR_API_KEY"

# Restart master (becomes replica)
docker compose -f docker-compose.local.yml start redis-master
```

### Automatic Recovery

When the failed master comes back online:

1. It automatically becomes a replica
2. Sentinels update their configuration
3. No manual intervention required

## Troubleshooting

### Common Issues

1. **Sentinel can't reach master**

   - Check Docker network connectivity
   - Verify Redis master is running
   - Check firewall settings

2. **Application can't connect**

   - Verify `REDIS_USE_SENTINEL=true`
   - Check Sentinel hosts configuration
   - Test direct Redis connection

3. **Failover not working**
   - Ensure quorum is met (2 Sentinels)
   - Check Sentinel logs for errors
   - Verify network connectivity between Sentinels

### Debugging Commands

```bash
# Check Sentinel configuration
docker exec -it sentinel-1 cat /usr/local/etc/redis/sentinel.conf

# Test Redis connectivity
docker exec -it redis-master redis-cli ping

# Check application logs
docker compose -f docker-compose.local.yml logs -f backend

# Monitor RQ dashboard
open http://localhost:9181
```

## Performance Considerations

### Memory Usage

- Master: 512MB configured limit
- Replica: 512MB configured limit
- Sentinels: Minimal memory usage

### Network Traffic

- Replication traffic between master and replica
- Sentinel monitoring traffic (minimal)
- Client connections through Sentinel discovery

### Persistence

- RDB snapshots: Every 15 minutes or 10K changes
- AOF: Every second (fsync)
- Both master and replica persist data

## Security Considerations

### Network Security

- All services run in isolated Docker network
- Sentinel ports exposed only for monitoring
- No authentication required in development

### Production Recommendations

- Enable Redis AUTH for production
- Use TLS for replication traffic
- Restrict network access to Redis ports
- Monitor Sentinel logs for security events

## Backup and Recovery

### Data Backup

- RDB files automatically created
- AOF provides point-in-time recovery
- Both master and replica maintain persistence

### Disaster Recovery

1. If both Redis instances fail:

   - Restore from RDB/AOF files
   - Restart services
   - Sentinels will detect and configure

2. If Sentinels fail:
   - Redis continues operating
   - No automatic failover until Sentinels restart
   - Manual intervention may be required

## Migration Guide

### From Single Redis to Sentinel

1. **Backup existing data**:

   ```bash
   docker exec redis redis-cli BGSAVE
   ```

2. **Update environment variables**:

   ```bash
   REDIS_USE_SENTINEL=true
   REDIS_SENTINEL_HOSTS=sentinel-1:26379,sentinel-2:26379,sentinel-3:26379
   ```

3. **Deploy new configuration**:

   ```bash
   docker compose -f docker-compose.local.yml up -d --build --force-recreate
   ```

4. **Verify operation**:
   ```bash
   curl -X GET "http://localhost:8000/api/v1/dlq/redis/sentinel-info" \
     -H "X-API-Key: YOUR_API_KEY"
   ```

### Rollback Plan

If issues occur, rollback by:

1. Set `REDIS_USE_SENTINEL=false`
2. Update `REDIS_HOST=redis-master`
3. Restart services

## Monitoring and Alerting

### Key Metrics to Monitor

- Redis master/replica lag
- Sentinel failover events
- Connection pool utilization
- Queue processing rates
- Failed job counts

### Recommended Alerts

- Redis master down
- Replication lag > 10 seconds
- Sentinel failover occurred
- High connection failure rate
- Queue processing stopped

### Next Steps

After implementing Sentinel:

1. **Task 12.2**: Set up read-only replica connections for load distribution
2. **Task 12.3**: Implement automated backup and disaster recovery
3. **Task 12.4**: Evaluate Redis Cluster for very high scale
4. **Performance Testing**: Test failover scenarios under load
5. **Monitoring**: Set up comprehensive monitoring and alerting

#### Sentinel Quorum Best Practices

Currently, the Sentinel quorum is set to `1` with two Sentinel nodes to allow failover even if one Sentinel is unreachable. For enhanced fault tolerance and to mitigate the risk of false positives in a production environment, it is strongly recommended to deploy an **odd number of Sentinel nodes (e.g., 3 or 5)** and configure the quorum to be `(N/2) + 1` (where N is the number of Sentinels), ensuring a clear majority for failover decisions. This will be considered as a future improvement.
