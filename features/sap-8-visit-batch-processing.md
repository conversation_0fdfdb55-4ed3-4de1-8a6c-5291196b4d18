# Sprint Planning: Batch Processing Implementation

## Higher Goal of the Change

✅ **Goal**: To enable the efficient and reliable processing of very large medical audio recordings by transforming the existing multi-file endpoint into a robust, scalable batch processing system that can handle medical visits with hundreds of audio files.

### **Very High-Level Rationales**:

- ✅ **Scalability**: Allow the system to handle medical visits with hundreds of audio files (e.g., 105 files) by processing them in manageable batches of up to 25 files each, without overwhelming the API or backend services.
- ✅ **Reliability & Resilience**: Improve the stability of the processing pipeline by breaking down large visits into smaller, manageable batches. This allows for better error isolation (fail-fast at batch level) and more predictable resource utilization.
- ✅ **Performance**: Leverage parallel processing of individual audio batches while maintaining the integrity of the complete medical visit through proper sequencing and concatenation.
- ✅ **Enhanced User Experience**: Provide clients with immediate feedback upon batch submission, continuous progress updates via webhooks, and clear communication regarding the success or failure of the entire visit processing job.

### **Core Concept**:

A **Visit Job** represents one medical visit that may contain multiple batches of audio files:

- Client creates one Visit Job ID for the entire medical visit
- Client uploads multiple batches (max 25 files per batch) using the same Visit Job ID
- System processes batches in parallel while maintaining order
- After all batches complete and finish signal is received, system concatenates all audio segments
- Complete audio is processed through the medical pipeline to produce one final visit report

**Example Flow**: Visit with 105 files = 5 batches (25+25+25+25+5 files) → All use same Visit Job ID → Parallel processing → Concatenation → Medical analysis → One visit report

### **Redis Infrastructure & High Availability Implementation**:

Given the critical nature of medical audio processing and the substantial processing time for large visits (potentially hours), **Redis High Availability has been successfully implemented** to prevent data loss and ensure system resilience:

#### **Implemented Redis HA Architecture**:

1. **Redis Sentinel Configuration**:

   - **3 Sentinel nodes** (`acva-ai-sentinel-1`, `acva-ai-sentinel-2`, `acva-ai-sentinel-3`) deployed for automatic master failover
   - **Quorum set to 2** to ensure clear majority decisions with 3 Sentinels
   - **Automatic master discovery** and failover logic implemented in `QueueConfig`
   - **30-second down-after-milliseconds** with 180-second failover timeout

2. **Redis Master-Replica Replication**:

   - **Master-Replica topology** configured with continuous data replication
   - **Automatic promotion** of replica to master during failover events
   - **Read-write separation** with master handling writes and replica available for reads
   - **Connection pooling** with master/replica awareness in application layer

3. **Data Persistence & Durability**:

   - **RDB Snapshots**: Point-in-time snapshots taken automatically:
     - Every 15 minutes if at least 1 key changes
     - Every 5 minutes if at least 10 keys change
     - Every 1 minute if at least 10,000 keys change
   - **AOF (Append Only File)**: Write operations logged every second (`appendfsync everysec`)
   - **Docker volume persistence**: All Redis data stored on persistent Docker volumes for container restart durability

4. **Application Integration**:
   - **Sentinel-aware connections**: Both `backend` and `rq-worker` services configured with `REDIS_USE_SENTINEL=true`
   - **Automatic master discovery**: Applications connect via Sentinels and automatically discover current master
   - **Failover resilience**: RQ Dashboard and workers automatically reconnect to new master after failover
   - **Graceful degradation**: System continues operating during brief failover windows (~30 seconds)

#### **High Availability Benefits Achieved**:

- **Eliminated Single Point of Failure**: Redis master failures now trigger automatic failover to replica
- **Data Loss Prevention**: Combination of replication and persistence ensures minimal data loss (maximum 1 second of commands)
- **Automatic Recovery**: System recovers from Redis failures without manual intervention
- **Production Readiness**: Redis infrastructure now suitable for production medical systems with high availability requirements

## API Design

### **Visit Job Lifecycle**:

1.  **Create Visit Job**: `POST /visit-jobs/create`

    - Returns: `visit_job_id` (used for all subsequent operations)
    - Purpose: Creates a container for all batches belonging to one medical visit

2.  **Submit Batches**: `POST /visit-jobs/{visit_job_id}/batches` (called multiple times)

    - Each call submits up to 25 files
    - All batches reference the same `visit_job_id`
    - Batches are queued and processed in parallel by workers while the job is in `RECEIVING_BATCHES` status.

3.  **Signal Completion**: `POST /visit-jobs/{visit_job_id}/finish`

    - Indicates all batches have been submitted.
    - The job transitions to `WAITING_FOR_FINISH` status.
    - Triggers report generation only when all batches have completed processing.

4.  **Monitor Progress**: `GET /visit-jobs/{visit_job_id}/status`
    - Shows progress across all batches
    - Indicates when concatenation and report generation begin

### **Data Model Hierarchy**:

```
VisitJob (visit_job_id)
├── Batch 1 (batch_id_1) → 25 files → Processed Audio Segment 1
├── Batch 2 (batch_id_2) → 25 files → Processed Audio Segment 2
├── Batch 3 (batch_id_3) → 25 files → Processed Audio Segment 3
├── Batch 4 (batch_id_4) → 25 files → Processed Audio Segment 4
└── Batch 5 (batch_id_5) → 5 files  → Processed Audio Segment 5
                                     ↓
                            ProcessVisitReportGenerationJob
                            (Assembly + Report Generation)
                                     ↓
                            Single VisitReport
```

**Workflow**:

- Batches processed in parallel → All complete → Single job handles assembly + report generation

## Epic: Transform Process Visit Endpoint to Support Batch Processing

### **Sprint 1: Infrastructure & Core Setup** ✅

#### ✅ **Story 1: Queue Infrastructure Setup**

- ✅ **Task 1.1**: Add Redis service to docker-compose.yml
  - ✅ Configure Redis container with persistence
  - ✅ Set up health checks
  - ✅ Configure Redis connection parameters
- ✅ **Task 1.2**: Install and configure RQ (Redis Queue)
  - ✅ Add RQ dependencies to requirements.txt
  - ✅ Create RQ configuration module
  - ✅ Set up RQ connection management
- ✅ **Task 1.3**: Create RQ worker service
  - ✅ Add RQ worker container to docker-compose
  - ✅ Configure worker startup script
  - ✅ Set up worker process management

#### ✅ **Story 2: Database Schema Extensions**

- ✅ **Task 2.1**: Create VisitJob model
  - ✅ Define VisitJob Pydantic model with all required fields
  - ✅ Add validation rules and constraints
  - ✅ Create model serialization methods
- ✅ **Task 2.2**: Create Batch model
  - ✅ Define Batch model for individual batch tracking
  - ✅ Add batch metadata and status fields
  - ✅ Implement batch ordering logic
- ✅ **Task 2.3**: Extend MongoDB collections
  - ✅ Add visit_jobs collection with indexes
  - ✅ Add batches collection with indexes
  - ✅ Create CRUD operations for visit jobs and batches

#### ✅ **Story 3: Core Worker Framework**

- ✅ **Task 3.1**: Create base worker classes
  - ✅ Implement visit-specific base worker with common functionality
  - ✅ Add error handling and logging framework
  - ✅ Create worker lifecycle management
- ✅ **Task 3.2**: Implement visit batch processing worker
  - ✅ Create worker for individual batch audio processing
  - ✅ Add audio concatenation and normalization logic
  - ✅ Implement batch result storage
- ✅ **Task 3.3**: Create visit job coordination utilities
  - ✅ Implement visit job status checking utilities
  - ✅ Add cross-worker communication helpers
  - ✅ Create cleanup and resource management functions

---

### **Sprint 2: Batch Processing Core Logic** ✅

#### ✅ **Story 4: Visit Job & Batch Submission Endpoints**

- ✅ **Task 4.1**: Create visit job creation endpoint
  - ✅ Implement POST /visit-jobs/create endpoint (creates Visit Job ID)
  - ✅ Add visit job validation and configuration
  - ✅ Generate unique Visit Job ID for entire medical visit
- ✅ **Task 4.2**: Create batch submission endpoint
  - ✅ Implement POST /visit-jobs/{visit_job_id}/batches endpoint
  - ✅ Add batch validation (max 25 files per batch, file types)
  - ✅ Associate batches with Visit Job ID
  - ✅ Implement batch queuing logic with visit context
- ✅ **Task 4.3**: Create finish signal endpoint
  - ✅ Implement POST /visit-jobs/{visit_job_id}/finish endpoint
  - ✅ Add finish signal validation for entire visit
  - ✅ Implement dependency setup for visit report generation
- ✅ **Task 4.4**: Add visit job status endpoints
  - ✅ Implement GET /visit-jobs/{visit_job_id}/status
  - ✅ Add detailed progress tracking across all batches
  - ✅ Create visit-level status reporting

#### ✅ **Story 5: Batch Processing Logic**

- ✅ **Task 5.1**: Implement batch audio processing
  - ✅ Create process_visit_batch_audio function (processes individual batches)
  - ✅ Add audio file handling and validation
  - ✅ Store processed batch segments (NOT concatenate yet)
- ✅ **Task 5.2**: Add timestamp and ordering validation
  - ✅ Extract timestamps from audio filenames
  - ✅ Implement batch ordering validation within visit
  - ✅ Add batch sequence conflict detection
- ✅ **Task 5.3**: Implement batch result storage
  - ✅ Store processed audio segments temporarily by visit job
  - ✅ Add batch completion tracking per visit
  - ✅ Implement result metadata storage with visit context

#### ✅ **Story 6: Visit Job Coordination Logic**

- ✅ **Task 6.1**: Implement visit job state management
  - ✅ Create functions to track batch completion across entire visit
  - ✅ Add visit job status updates and transitions
  - ✅ Implement atomic state changes for visit-level operations
- ✅ **Task 6.2**: Add visit-level batch dependency management
  - ✅ Implement batch ordering dependencies within visit job
  - ✅ Add finish signal dependency logic for entire visit
  - ✅ Create dependency validation functions across all batches
- ✅ **Task 6.3**: Create visit report generation trigger logic
  - ✅ Implement automatic report generation detection (all batches + finish signal)
  - ✅ Add visit report generation job queuing (concatenation + report generation)
  - ✅ Create report generation prerequisites validation for entire visit

#### ✅ **Story 7: Visit Assembly Implementation**

- ✅ **Task 7.1**: Create visit assembly worker
  - ✅ Implement assemble_visit function (concatenates all batches)
  - ✅ Add integration with existing report generation pipeline
  - ✅ Create visit assembly job queuing
- ✅ **Task 7.2**: Add visit assembly validation
  - ✅ Implement batch result collection from all batches in visit job
  - ✅ Add timestamp-based ordering validation across all batches
  - ✅ Create complete visit audio concatenation from all batch segments
- ✅ **Task 7.3**: Integrate with report generation pipeline
  - ✅ Modify process_visit integration to accept assembled visit audio
  - ✅ Add visit job metadata to visit reports
  - ✅ Maintain single VisitReport output per visit job

#### ✅ **Story 8: Job Architecture Modernization**

- ✅ **Task 8.1**: Create class-based job framework
  - ✅ Implement `BaseJob` abstract class with standardized execution pattern
  - ✅ Add job logging, error handling, and timing functionality
  - ✅ Create job priority and configuration management
- ✅ **Task 8.2**: Implement job registration system
  - ✅ Create `@register_job` decorator for automatic job discovery
  - ✅ Implement `JobRegistry` for centralized job management
  - ✅ Add job enumeration and lookup functionality
- ✅ **Task 8.3**: Migrate existing workers to job classes
  - ✅ Convert `ProcessVisitBatchAudioJob` from function-based to class-based
  - ✅ Convert `ProcessVisitReportGenerationJob` from function-based to class-based with integrated batch assembly
- ✅ **Task 8.4**: Remove obsolete worker infrastructure
  - ✅ Delete obsolete function-based worker files (`visit_batch_worker.py`, `visit_assembly_worker.py`, etc.)
  - ✅ Remove old job registry with hardcoded string paths (`job_registry.py`)
  - ✅ Clean up circular imports and unused base worker classes
- ✅ **Task 8.5**: Update job enqueuing system
  - ✅ Replace hardcoded string paths with importable job function paths
  - ✅ Fix RQ job function import issues for reliable job execution
  - ✅ Update all enqueue calls to use class-based pattern

---

### **Sprint 3: Error Handling & DLQ Implementation**

#### ✅ **Story 8: Fail-Fast Error Handling**

- ✅ **Task 8.1**: Implement batch failure detection
  - ✅ Add error detection in batch processing (`_check_visit_job_should_fail()` in ProcessVisitBatchAudioJob)
  - ✅ Implement immediate job termination logic (jobs abort if other batches in visit have failed)
  - ✅ Create failure signal propagation (visit job status updated to FAILED when any batch fails)
- ✅ **Task 8.2**: Add cross-worker failure communication
  - ✅ Implement job failure status updates (VisitJobCoordinator updates visit job status to FAILED)
  - ✅ Add worker abortion mechanisms (batches check visit job status and abort if failed)
  - ✅ Create in-progress work cleanup (temporary files cleaned up in batch processing)
- ✅ **Task 8.3**: Implement resource cleanup on failure
  - ✅ Add cleanup for partially processed audio (temporary files removed in \_process_audio_batch)
  - ✅ Implement temporary file removal (os.remove() calls for temp files)
  - ✅ Create database cleanup procedures (batch status updated to FAILED, error messages stored)
  - ✅ **Fix cleanup bug**: Corrected MinIO cleanup methods to clean up correct directories (batch_results and visit_results)
    - ✅ Fixed `cleanup_batch_files()` to clean up `batch_results/{batch_id}/` instead of wrong path
    - ✅ Added `cleanup_visit_results()` method to clean up `visit_results/{visit_job_id}/` intermediate files
    - ✅ Updated assembly job to call both cleanup methods after successful processing
    - ✅ Verified cleanup works correctly - only final audio file remains after processing

#### ✅ **Story 9: Failed Job Management (Simplified)**

- ✅ **Task 9.1**: Set up failed job infrastructure
  - ✅ Use RQ's built-in failed job registry (no separate `failed_jobs` queue needed)
  - ✅ Add enhanced structured logging for Kibana analysis (`_handle_worker_exception` logs detailed error context)
  - ✅ Implement error context preservation (job ID, error message, traceback, and timing information logged)
- ✅ **Task 9.2**: Create failed job processing workflow
  - ✅ Implement automatic failed job routing to RQ's built-in failed registry
  - ✅ Add comprehensive structured logging with job context for Kibana analysis
  - ✅ Create failed job monitoring utilities (stats via `get_queue_stats()` and `get_dlq_stats()`)
- ✅ **Task 9.3**: Add failed job management endpoints
  - ✅ Implement DLQ job inspection endpoints (`GET /dlq/jobs`, `GET /dlq/jobs/{job_id}`)
  - ✅ Add manual requeue functionality (`POST /dlq/jobs/{job_id}/requeue`)
  - ✅ Create DLQ job statistics and reporting (`GET /dlq/stats`, `DELETE /dlq/jobs/{job_id}`, `POST /dlq/bulk-requeue`)

**Note**: This implementation uses RQ's built-in failed job registry as a DLQ. Error investigation is handled through structured logging with Kibana rather than job-based DLQ processing.

---

### **Sprint 4: Webhook System & Client Communication**

#### 🔶 **Story 10: Webhook Infrastructure**

- ✅ **Task 10.1**: Create webhook delivery system
  - ✅ Implement webhook HTTP client (WebhookService with httpx.AsyncClient)
  - ✅ Add webhook retry logic with exponential backoff (5 retries, exponential backoff up to 5 minutes)
  - ✅ Create webhook delivery tracking (comprehensive logging and return status)
- ✅ **Task 10.2**: Define webhook event types
  - ✅ Create webhook payload schemas (standardized payload with event_type, visit_job_id, status, timestamp, data)
  - ✅ Implement event type definitions (visit_completed, report_generation_started, report_generation_completed, assembly_started, etc.)
  - ✅ Add webhook authentication (X-Event-Type and X-Visit-Job-ID headers)
- ❌ **Task 10.3**: Add webhook configuration management
  - ❌ Implement webhook URL configuration
  - ❌ Add webhook subscription management
  - ❌ Create webhook delivery preferences

#### 🔶 **Story 11: Event-Driven Notifications**

- ❌ **Task 11.1**: Implement batch completion events
  - ❌ Add batch_completed webhook events (VisitJobCoordinator.update_batch_status doesn't send webhooks)
  - ❌ Create batch_failed event notifications (no batch-level webhook events implemented)
  - ❌ Implement batch progress updates (no batch progress webhook events)
- ✅ **Task 11.2**: Add job-level event notifications
  - ✅ Create job_completed webhook events (`visit_completed` sent in ProcessVisitReportGenerationJob)
  - 🔶 Add job_failed event notifications (`report_generation_failed` sent, but no general job_failed)
  - ✅ Implement assembly_started events (`assembly_started` sent in ProcessVisitReportGenerationJob)
- ❌ **Task 11.3**: Create webhook monitoring and debugging
  - ❌ Add webhook delivery logging (basic logging exists but no structured webhook-specific logs)
  - ❌ Implement webhook failure tracking (no persistent failure tracking)
  - ❌ Create webhook debugging endpoints (no debugging APIs)

---

### **Sprint 5: Production Infrastructure & Redis High Availability**

#### ✅ **Story 12: Redis Redundancy & High Availability**

- ✅ **Task 12.1**: Implement Redis Sentinel for automatic failover
  - ✅ Configure Redis Sentinel cluster (2 sentinel nodes minimum)
  - ✅ Update QueueConfig to support Sentinel-based connections
  - ✅ Add automatic master discovery and failover logic
  - ✅ Configure sentinel.conf with proper quorum settings
- ✅ **Task 12.2**: Set up Redis Master-Slave replication
  - ✅ Configure Redis master-slave replication topology
  - ✅ Add read-only slave nodes for load distribution
  - ✅ Implement connection pooling with master/slave awareness
  - ✅ Add replication lag monitoring and alerting
- ✅ **Task 12.3**: Redis backup and disaster recovery
  - ✅ Implement automated Redis persistence (RDB + AOF) - configured in redis-master.conf and redis-replica.conf
  - ✅ Set up Redis data persistence to Docker volumes for durability
  - ✅ Configure proper RDB snapshotting (save 900 1, save 300 10, save 60 10000)
  - ✅ Enable AOF logging (appendonly yes, appendfsync everysec) for data durability

#### [] **Story 13: Monitoring & Observability**

- [] **Task 13.1**: Add batch processing metrics
  - [] Implement batch processing time tracking
  - [] Add queue depth monitoring
  - [] Create failure rate metrics
- [] **Task 13.2**: Create health check endpoints
  - [] Implement system health checks
  - [] Add queue health monitoring (including Redis cluster health)
  - [] Create database connectivity checks
  - [] Add Redis Sentinel/cluster status monitoring
- [] **Task 13.3**: Add logging and alerting
  - [] Implement structured logging for batch processing
  - [] Add error alerting mechanisms
  - [] Create performance monitoring dashboards
  - [] Add Redis failover and replication lag alerts

#### [] **Story 14: Testing & Validation**

- [] **Task 14.1**: Create unit tests for batch processing
  - [] Test batch submission logic
  - [] Test error handling scenarios
  - [] Test audio assembly functionality
- [] **Task 14.2**: Add integration tests
  - [] Test end-to-end batch processing flow
  - [] Test webhook delivery
  - [] Test DLQ functionality
- [] **Task 14.3**: Implement load testing
  - [] Test with maximum batch sizes (25 files)
  - [] Test with multiple concurrent jobs
  - [] Test system resource limits
- [] **Task 14.4**: Redis failover testing
  - [] Test Redis master node failure scenarios
  - [] Validate automatic failover with Sentinel
  - [] Test job queue persistence during Redis failover
  - [] Verify RQ worker reconnection after Redis recovery

#### [] **Story 15: Documentation & Deployment**

- [] **Task 15.1**: Create API documentation
  - [] Document new batch processing endpoints
  - [] Add webhook event documentation
  - [] Create error handling documentation
- [] **Task 15.2**: Update deployment configuration
  - [] Update environment variables (Redis Sentinel endpoints, etc.)
  - [] Add production configuration with Redis HA
  - [] Create deployment scripts for Redis cluster setup
  - [] Configure Docker Compose for Redis Sentinel/clustering
- [] **Task 15.3**: Create operational runbooks
  - [] Document DLQ management procedures
  - [] Add troubleshooting guides
  - [] Create monitoring and alerting setup
  - [] Document Redis failover procedures and recovery steps
  - [] Add Redis cluster maintenance and scaling procedures

---

### **Sprint 6: Migration & Rollout**

#### [] **Story 16: Backward Compatibility & Migration**

- [] **Task 16.1**: Maintain existing endpoint compatibility
  - [] Ensure existing /process-visit endpoint still works
  - [] Add feature flags for batch processing
  - [] Create migration path for existing clients
- [] **Task 16.2**: Create batch processing client examples
  - [] Implement example client code
  - [] Add batch processing usage examples
  - [] Create migration guide for existing clients
- [] **Task 16.3**: Implement gradual rollout
  - [] Add feature toggles for batch processing
  - [] Create rollback procedures
  - [] Implement canary deployment strategy
- [] **Task 16.4**: Redis infrastructure migration
  - [] Plan zero-downtime migration from single Redis to Redis HA
  - [] Create migration scripts for existing queue data
  - [] Test rollback procedures for Redis infrastructure changes

---

## Definition of Done for Each Task:

- ✅ Code implemented and reviewed
- ✅ Unit tests written and passing
- ✅ Integration tests passing
- ✅ Documentation updated
- ✅ Error handling implemented
- ✅ Logging added
- ✅ Performance tested
- ✅ Security reviewed

## Sprint Dependencies:

- Sprint 1 must complete before Sprint 2 ✅
- Sprint 2 must complete before Sprint 3 and 4 ✅
- Sprint 3 and 4 can run in parallel
- Sprint 5 (Redis HA & Infrastructure) should start after Sprint 4 completes
- Sprint 6 depends on Sprint 5 completion for Redis infrastructure migration
- Redis redundancy testing (Story 14) depends on Redis HA implementation (Story 12)

---

### **Implementation Status**:

### **Completed**: ✅ Sprint 1 & Sprint 2 & Sprint 3

- ✅ Complete visit job architecture with proper data models
- ✅ Visit job creation, batch submission, and finish signal endpoints
- ✅ Parallel batch processing with visit-level coordination (no separate PROCESSING_BATCHES status)
- ✅ Visit assembly worker for concatenating all batches
- ✅ Integration with report generation pipeline
- ✅ Comprehensive error handling and state management (where a single batch failure leads to job failure)
- ✅ **Modern Class-Based Job Architecture**: Migrated from function-based workers to robust class-based job system
  - ✅ **BaseJob Framework**: Created abstract base class with standardized job execution, logging, and error handling
  - ✅ **Job Registration System**: Implemented `@register_job` decorator with automatic job discovery and registration
  - ✅ **Refactoring-Safe Jobs**: Eliminated hardcoded string paths - jobs are now type-safe and refactoring-friendly
  - ✅ **Consistent Job Pattern**: All background jobs follow the same class-based pattern with proper RQ integration
  - ✅ **Clean Architecture**: Removed obsolete worker files and circular dependencies for maintainable codebase
- ✅ **Fail-Fast Error Handling**: Complete implementation of batch failure detection, cross-worker communication, and resource cleanup
- ✅ **Dead Letter Queue System**: Full DLQ implementation with automatic failed job routing, enhanced structured logging, comprehensive management APIs for inspection, requeuing, statistics, and bulk operations

### **Mostly Complete**: 🔶 Sprint 4 (Webhook System)

- ✅ **Robust Webhook Infrastructure**: Complete HTTP client with exponential backoff retry logic and comprehensive error handling
- ✅ **Event Type Definitions**: Standardized webhook payload schemas and event types with proper authentication headers
- ✅ **Job-Level Notifications**: `visit_completed`, `assembly_started`, `report_generation_started/completed/failed` events implemented
- ❌ **Missing**: Batch-level webhook events (`batch_completed`, `batch_failed`), webhook debugging endpoints, and configuration management

### **Technical Benefits of Job Architecture**:

- **🔒 Type Safety**: Jobs are refactoring-safe with proper IDE support and static analysis
- **📊 Standardized Monitoring**: All jobs have consistent logging, timing, and error reporting
- **🔄 Easier Maintenance**: Single inheritance hierarchy makes adding new jobs straightforward
- **🚫 No String Paths**: Eliminated brittle hardcoded function paths
- **⚡ Better Performance**: Reduced circular imports and cleaner dependency management
- **🧪 Testability**: Job classes are easier to unit test with dependency injection
- **📈 Scalability**: Foundation for advanced features like job priorities, retries, and monitoring
- **🚀 Reduced Latency**: Direct path from batch completion to report generation
- **💡 Simplified Architecture**: Single job handles both batch assembly and report generation
- **🔧 Fewer Failure Points**: Streamlined workflow reduces potential failure scenarios

### **Remaining Work**: Complete Sprint 4

- Add batch-level webhook events in `VisitJobCoordinator.update_batch_status()`
- Create webhook debugging and monitoring endpoints
- Implement webhook configuration management features
