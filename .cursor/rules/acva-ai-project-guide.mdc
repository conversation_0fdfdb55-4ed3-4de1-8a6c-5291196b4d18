---
description: Acva AI project guidelines
globs: **/*.py, acva_ai/**/*.py
---

# ACVA AI Project Development Guide

This guide contains project-specific rules, workflows, and best practices for developing the ACVA AI medical transcription processing system.

## 🔧 Development Environment Setup

### Python Environment

**RULE 1: Always source the virtual environment before running Python commands**

```bash
# Always run this first when working with Python
source .venv/bin/activate

# Then you can run Python commands
python -m pytest
python -c "import acva_ai; print('OK')"
```

### Docker Development Workflow

**RULE 2: Use Docker Compose and rebuild containers after making changes**

```bash
# Standard rebuild command after code changes
docker compose -f docker-compose.local.yml up -d backend rq-worker --build --force-recreate

# For backend-only changes
docker compose -f docker-compose.local.yml up -d backend --build --force-recreate

# For worker-only changes
docker compose -f docker-compose.local.yml up -d rq-worker --build --force-recreate

# View logs
docker compose logs -f backend
docker compose logs -f rq-worker
```

## 🏗️ Architecture Overview

### Core Components

- **FastAPI Backend**: Main API server (`acva_ai/api/`)
- **RQ Workers**: Background job processing (`acva_ai/jobs/`, `acva_ai/services/`)
- **MongoDB**: Document storage for visit data
- **Redis**: Queue management and caching
- **MinIO**: Object storage for audio files
- **Qdrant**: Vector database for RAG operations

### Key Services

- `QueueConfig`: Manages RQ queues (batch processing, report generation)
- `VisitJobCoordinator`: Orchestrates visit processing workflows
- `WorkerService`: Handles background job execution
- `RAG Service`: Retrieval-augmented generation for medical insights

## 📝 Development Rules & Best Practices

### Code Quality

1. **Type Hints**: Always use type hints for function parameters and return values
2. **Error Handling**: Wrap external service calls in try-catch blocks with proper logging
3. **Logging**: Use structured logging with context (`logger.error(msg, extra={...})`)
4. **Imports**: Use absolute imports (`from acva_ai.services.queue_config import queue_config`)

### API Development

1. **Authentication**: All API routes require `X-API-Key` header
2. **Validation**: Use Pydantic models for request/response validation
3. **Error Responses**: Return structured error responses with appropriate HTTP status codes
4. **Documentation**: Include comprehensive docstrings for all endpoints

### Queue Management

1. **Job Design**: Inherit from `BaseJob` for all background jobs
2. **Error Handling**: Failed jobs automatically go to RQ's failed job registry
3. **Monitoring**: Use RQ Dashboard at `http://localhost:9181` for queue monitoring
4. **Timeouts**: Set appropriate `job_timeout` values (default: 1800s)

### Database Operations

1. **MongoDB**: Use async operations with proper connection management
2. **Transactions**: Use transactions for multi-document operations
3. **Indexing**: Ensure proper indexing for query performance
4. **Validation**: Use Pydantic models for data validation

## 🔍 Testing & Debugging

### Local Testing Commands

```bash
# Run tests (remember to source venv first!)
source .venv/bin/activate
python -m pytest tests/

# Test specific API endpoints
curl -X GET "http://localhost:8000/api/v1/failed-jobs/stats" \
  -H "X-API-Key: YOUR_API_KEY_HERE" # Replace with your actual API key

# Check queue status
curl -X GET "http://localhost:8000/api/v1/queues/status" \
  -H "X-API-Key: YOUR_API_KEY_HERE" # Replace with your actual API key
```

### Common Debugging Steps

1. **Check Container Logs**: `docker compose logs -f [service]`
2. **Verify Services**: Ensure all dependencies (MongoDB, Redis, etc.) are healthy
3. **RQ Dashboard**: Monitor job execution at `http://localhost:9181`
4. **API Documentation**: Access Swagger UI at `http://localhost:8000/docs`

### Environment Variables

Key environment variables for local development:

```bash
REDIS_HOST=localhost  # When running Python scripts outside Docker
MONGODB_URL=mongodb://localhost:27017/acva_ai
MINIO_ENDPOINT=localhost:9000
QDRANT_HOST=localhost
QDRANT_PORT=6333
```

## 🚀 Deployment Workflow

### Pre-deployment Checklist

1. ✅ All tests pass
2. ✅ No linter errors
3. ✅ Docker containers build successfully
4. ✅ API endpoints respond correctly
5. ✅ Background jobs process without errors

### Production Considerations

1. **Scaling**: RQ workers can be scaled horizontally
2. **Monitoring**: Set up proper logging aggregation and metrics
3. **Health Checks**: All services include health check endpoints
4. **Security**: API keys and secrets managed through environment variables

## 🔧 Common Issues & Solutions

### "Command not found: docker-compose"

**Solution**: Use `docker compose` (newer syntax) instead of `docker-compose`

### Redis Connection Errors

**Solution**:

- In Docker: Use `redis` as hostname
- Outside Docker: Use `localhost` and set `REDIS_HOST=localhost`

### Worker Not Processing Jobs

**Solution**:

1. Rebuild worker container: `docker compose -f docker-compose.local.yml up -d rq-worker --build --force-recreate`
2. Check worker logs for errors
3. Verify Redis connectivity

### Failed Jobs Not Appearing

**Solution**:

- Check RQ Dashboard at `
