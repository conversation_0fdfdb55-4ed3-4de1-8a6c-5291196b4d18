from typing import List, Literal, Optional
from uuid import UUID

from pydantic import BaseModel, model_validator


class Medication(BaseModel):
    medication_name: Optional[str] = None
    medication_context: Optional[str] = None
    prospect: Optional[str] = (None,)
    reference_url: Optional[str] = None
    extracted_rag_name: Optional[str] = None


class MedicationList(BaseModel):
    medications: List[Medication] = []
