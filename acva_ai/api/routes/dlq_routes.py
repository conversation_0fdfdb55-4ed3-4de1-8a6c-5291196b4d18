import logging
from typing import List, Optional

from fastapi import APIRouter, Depends, HTTPException, Query
from fastapi.responses import JSONResponse

from acva_ai.models.dlq_job import DLQJobInfo, DLQJobResponse, DLQStatsResponse
from acva_ai.services.queue_config import queue_config
from acva_ai.utils.security_service import SecurityService

logger = logging.getLogger(__name__)

dlq_router = APIRouter(dependencies=[Depends(SecurityService.get_api_key)])


@dlq_router.get("/jobs", response_model=List[DLQJobResponse])
async def get_dlq_jobs(
    limit: int = Query(50, ge=1, le=200, description="Number of jobs to return"),
    offset: int = Query(0, ge=0, description="Number of jobs to skip"),
):
    """
    Get failed jobs from the Dead Letter Queue with pagination.

    Returns detailed information about failed jobs including error context,
    timing information, and requeue capability assessment.
    """
    try:
        jobs = queue_config.get_dlq_jobs(limit=limit, offset=offset)

        responses = []
        for job in jobs:
            # Convert to DLQJobInfo
            try:
                from datetime import datetime

                job_info = DLQJobInfo(
                    job_id=job.get("job_id", ""),
                    original_queue=job.get("origin_queue", "unknown"),
                    function_name=job.get("function_name", ""),
                    args=eval(job.get("args", "[]")) if job.get("args") else [],
                    kwargs=eval(job.get("kwargs", "{}")) if job.get("kwargs") else {},
                    error_message=job.get("error_message", ""),
                    error_type=job.get("error_type", ""),
                    traceback=job.get("traceback"),
                    created_at=(
                        datetime.fromisoformat(job["created_at"].replace("Z", "+00:00"))
                        if job.get("created_at")
                        else datetime.now()
                    ),
                    failed_at=(
                        datetime.fromisoformat(job["failed_at"].replace("Z", "+00:00"))
                        if job.get("failed_at")
                        else datetime.now()
                    ),
                    enqueued_at=(
                        datetime.fromisoformat(
                            job["enqueued_at"].replace("Z", "+00:00")
                        )
                        if job.get("enqueued_at")
                        else None
                    ),
                    started_at=(
                        datetime.fromisoformat(job["started_at"].replace("Z", "+00:00"))
                        if job.get("started_at")
                        else None
                    ),
                    retry_count=int(job.get("retry_count", 0)),
                    worker_name=job.get("worker_name"),
                    metadata=(
                        job.get("metadata", {})
                        if isinstance(job.get("metadata", {}), dict)
                        else {}
                    ),
                )

                # Assess requeue capability
                can_requeue = True
                requeue_reason = None

                if not job_info.function_name:
                    can_requeue = False
                    requeue_reason = "Missing function name"
                elif job_info.retry_count >= 5:
                    can_requeue = False
                    requeue_reason = "Maximum retries exceeded"

                responses.append(
                    DLQJobResponse(
                        job_info=job_info,
                        can_requeue=can_requeue,
                        requeue_reason=requeue_reason,
                    )
                )

            except Exception as parse_error:
                logger.error(
                    f"Failed to parse DLQ job {job.get('job_id', 'unknown')}: {parse_error}"
                )
                continue

        return responses

    except Exception as e:
        logger.error(f"Error getting DLQ jobs: {e}")
        raise HTTPException(status_code=500, detail="Failed to retrieve DLQ jobs")


@dlq_router.get("/jobs/{job_id}", response_model=DLQJobResponse)
async def get_dlq_job(job_id: str):
    """
    Get detailed information about a specific failed job in the DLQ.
    """
    try:
        job_details = queue_config.get_dlq_job_details(job_id)

        if not job_details:
            raise HTTPException(status_code=404, detail="DLQ job not found")

        from datetime import datetime

        job_info = DLQJobInfo(
            job_id=job_details.get("job_id", job_id),
            original_queue=job_details.get("origin_queue", "unknown"),
            function_name=job_details.get("function_name", ""),
            args=eval(job_details.get("args", "[]")) if job_details.get("args") else [],
            kwargs=(
                eval(job_details.get("kwargs", "{}"))
                if job_details.get("kwargs")
                else {}
            ),
            error_message=job_details.get("error_message", ""),
            error_type=job_details.get("error_type", ""),
            traceback=job_details.get("traceback"),
            created_at=(
                datetime.fromisoformat(job_details["created_at"].replace("Z", "+00:00"))
                if job_details.get("created_at")
                else datetime.now()
            ),
            failed_at=(
                datetime.fromisoformat(job_details["failed_at"].replace("Z", "+00:00"))
                if job_details.get("failed_at")
                else datetime.now()
            ),
            enqueued_at=(
                datetime.fromisoformat(
                    job_details["enqueued_at"].replace("Z", "+00:00")
                )
                if job_details.get("enqueued_at")
                else None
            ),
            started_at=(
                datetime.fromisoformat(job_details["started_at"].replace("Z", "+00:00"))
                if job_details.get("started_at")
                else None
            ),
            retry_count=int(job_details.get("retry_count", 0)),
            worker_name=job_details.get("worker_name"),
            metadata=(
                job_details.get("metadata", {})
                if isinstance(job_details.get("metadata", {}), dict)
                else {}
            ),
        )

        # Assess requeue capability
        can_requeue = True
        requeue_reason = None

        if not job_info.function_name:
            can_requeue = False
            requeue_reason = "Missing function name"
        elif job_info.retry_count >= 5:
            can_requeue = False
            requeue_reason = "Maximum retries exceeded"

        return DLQJobResponse(
            job_info=job_info, can_requeue=can_requeue, requeue_reason=requeue_reason
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting DLQ job {job_id}: {e}")
        raise HTTPException(status_code=500, detail="Failed to retrieve DLQ job")


@dlq_router.post("/jobs/{job_id}/requeue")
async def requeue_dlq_job(job_id: str):
    """
    Requeue a failed job from the DLQ back to its original queue or a specified queue.

    The job will be removed from the DLQ and added back to the processing queue.
    """
    try:
        # Get job details first to validate
        job_details = queue_config.get_dlq_job_details(job_id)
        if not job_details:
            raise HTTPException(status_code=404, detail="DLQ job not found")

        # Check if job can be requeued
        if not job_details.get("function_name"):
            raise HTTPException(
                status_code=400, detail="Cannot requeue job: missing function name"
            )

        retry_count = int(job_details.get("retry_count", 0))
        if retry_count >= 5:
            raise HTTPException(
                status_code=400, detail="Cannot requeue job: maximum retries exceeded"
            )

        success = queue_config.requeue_dlq_job(job_id)

        if success:
            return JSONResponse(
                {
                    "message": f"Successfully requeued job {job_id}",
                    "job_id": job_id,
                }
            )
        else:
            raise HTTPException(status_code=500, detail="Failed to requeue job")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error requeuing DLQ job {job_id}: {e}")
        raise HTTPException(status_code=500, detail="Failed to requeue job")


@dlq_router.delete("/jobs/{job_id}")
async def clear_dlq_job(job_id: str):
    """
    Remove a job from the DLQ without requeuing it.

    This permanently removes the job from tracking.
    """
    try:
        # Check if job exists
        job_details = queue_config.get_dlq_job_details(job_id)
        if not job_details:
            raise HTTPException(status_code=404, detail="DLQ job not found")

        success = queue_config.clear_dlq_job(job_id)

        if success:
            return JSONResponse(
                {
                    "message": f"Successfully cleared job {job_id} from DLQ",
                    "job_id": job_id,
                }
            )
        else:
            raise HTTPException(status_code=500, detail="Failed to clear job from DLQ")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error clearing DLQ job {job_id}: {e}")
        raise HTTPException(status_code=500, detail="Failed to clear job from DLQ")


@dlq_router.get("/stats", response_model=DLQStatsResponse)
async def get_dlq_stats():
    """
    Get comprehensive statistics about the Dead Letter Queue.

    Includes job counts by function and error type, age statistics, and retry patterns.
    """
    try:
        stats = queue_config.get_dlq_stats()

        return DLQStatsResponse(
            total_failed_jobs=stats.get("total_failed_jobs", 0),
            batch_processing_failed=stats.get("batch_processing_failed", 0),
            report_generation_failed=stats.get("report_generation_failed", 0),
        )

    except Exception as e:
        logger.error(f"Error getting DLQ stats: {e}")
        raise HTTPException(status_code=500, detail="Failed to retrieve DLQ statistics")


@dlq_router.post("/bulk-requeue")
async def bulk_requeue_dlq_jobs(
    function_name: Optional[str] = Query(
        None, description="Only requeue jobs from this function"
    ),
    error_type: Optional[str] = Query(
        None, description="Only requeue jobs with this error type"
    ),
    max_jobs: int = Query(
        10, ge=1, le=100, description="Maximum number of jobs to requeue"
    ),
):
    """
    Bulk requeue multiple failed jobs from the DLQ based on filters.

    Useful for requeuing jobs after fixing systemic issues.
    """
    try:
        jobs = queue_config.get_dlq_jobs(
            limit=max_jobs * 2
        )  # Get more to account for filtering

        requeued_jobs = []
        failed_jobs = []

        for job in jobs:
            # Apply filters
            if function_name and job.get("function_name") != function_name:
                continue
            if error_type and job.get("error_type") != error_type:
                continue

            # Check if we've reached the limit
            if len(requeued_jobs) >= max_jobs:
                break

            job_id = job.get("job_id")
            if not job_id:
                continue

            # Check if job can be requeued
            if not job.get("function_name"):
                failed_jobs.append(
                    {"job_id": job_id, "reason": "Missing function name"}
                )
                continue

            retry_count = int(job.get("retry_count", 0))
            if retry_count >= 5:
                failed_jobs.append(
                    {"job_id": job_id, "reason": "Maximum retries exceeded"}
                )
                continue

            # Attempt to requeue
            success = queue_config.requeue_dlq_job(job_id)
            if success:
                requeued_jobs.append(job_id)
            else:
                failed_jobs.append(
                    {"job_id": job_id, "reason": "Requeue operation failed"}
                )

        return JSONResponse(
            {
                "message": f"Bulk requeue completed: {len(requeued_jobs)} successful, {len(failed_jobs)} failed",
                "requeued_jobs": requeued_jobs,
                "failed_jobs": failed_jobs,
                "filters_applied": {
                    "function_name": function_name,
                    "error_type": error_type,
                    "max_jobs": max_jobs,
                },
            }
        )

    except Exception as e:
        logger.error(f"Error in bulk requeue: {e}")
        raise HTTPException(status_code=500, detail="Failed to perform bulk requeue")
